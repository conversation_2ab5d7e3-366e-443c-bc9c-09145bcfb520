import 'package:flutter/material.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/reels/presentation/views/reels_page.dart';
import 'package:dio/dio.dart';
import 'package:hive/hive.dart';

class FavoriteScreen extends StatefulWidget {
  const FavoriteScreen({super.key});

  @override
  _FavoriteScreenState createState() => _FavoriteScreenState();
}

class _FavoriteScreenState extends State<FavoriteScreen> {
  bool _isLoading = true;
  bool _hasError = false;
  List<Map<String, dynamic>> _favorites = [];
  late final DioConsumer _dioConsumer;

  @override
  void initState() {
    super.initState();
    _dioConsumer = DioConsumer(
      dio: getIt<Dio>(),
      profileBox: getIt<Box<UserEntity>>(),
    );
    _fetchFavorites();
  }

  Future<void> _fetchFavorites() async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });

      final response = await _dioConsumer.get('/api/favorite/list');

      if (response['data'] != null) {
        final List<Map<String, dynamic>> favorites = (response['data'] as List)
            .map((item) => item['item'] as Map<String, dynamic>)
            .toList();

        setState(() {
          _favorites = favorites;
          _isLoading = false;
        });
      } else {
        setState(() {
          _hasError = true;
          _isLoading = false;
        });
      }
    } catch (e) {
      print("Error fetching favorites: $e");
      setState(() {
        _hasError = true;
        _isLoading = false;
      });
    }
  }

  void _navigateToReels(Map<String, dynamic> item) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ReelsPage(
          searchResults: [item],
          searchQuery: item['title'],
          serviceCategoryId: item['service_category_id'] ?? 0,
        ),
      ),
    );
  }

  Widget _buildLoading() {
    return const Center(
      child: CircularProgressIndicator(color: AppColors.white),
    );
  }

  Widget _buildError() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, color: AppColors.white, size: 50),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ في تحميل المفضلة',
            style: AppTextStyles.font16Medium.copyWith(color: AppColors.white),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _fetchFavorites,
            child: const Text(
              'إعادة المحاولة',
              style: AppTextStyles.font14Medium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmpty() {
    return Center(
      child: Text(
        'لا يوجد عناصر في المفضلة',
        style: AppTextStyles.font16Medium.copyWith(color: AppColors.white),
      ),
    );
  }

  Widget _buildList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _favorites.length,
      itemBuilder: (context, index) {
        final item = _favorites[index];
        return Card(
          color: AppColors.lightGrey10,
          child: ListTile(
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 8,
            ),
            leading: item['image'] != null
                ? Image.network(
              item['image'],
              width: 60,
              height: 60,
              fit: BoxFit.cover,
            )
                : const Icon(Icons.favorite, color: AppColors.red),
            title: Text(
              item['title'] ?? 'No Title',
              style: AppTextStyles.font16Bold.copyWith(color: AppColors.white),
            ),
            subtitle: Text(
              item['content'] ?? 'No Description',
              style: AppTextStyles.font14Medium.copyWith(color: AppColors.white),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            trailing: Text(
              '${item['price'] ?? 'N/A'} ر.س',
              style: AppTextStyles.font14Bold.copyWith(color: AppColors.white),
            ),
            onTap: () => _navigateToReels(item),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المفضلة', style: AppTextStyles.font18Bold),
        backgroundColor: AppColors.black,
      ),
      backgroundColor: AppColors.black,
      body: _isLoading
          ? _buildLoading()
          : _hasError
          ? _buildError()
          : _favorites.isEmpty
          ? _buildEmpty()
          : _buildList(),
    );
  }
}