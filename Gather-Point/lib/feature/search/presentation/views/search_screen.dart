import 'package:flutter/material.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/reels/presentation/views/reels_page.dart'; // Import the ReelsPage
import 'package:dio/dio.dart';
import 'package:hive/hive.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  _SearchScreenState createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  bool _isLoading = false; // Track loading state
  List<Map<String, dynamic>> _searchResults = []; // Store search results
  late final DioConsumer dioConsumer; // Declare DioConsumer

  @override
  void initState() {
    super.initState();
    // Initialize DioConsumer using getIt dependencies
    dioConsumer = DioConsumer(
      dio: getIt<Dio>(),
      profileBox: getIt<Box<UserEntity>>(),
    );
  }

  // Fetch search results from the API using DioConsumer
  Future<void> _fetchSearchResults(String query) async {
    setState(() {
      _isLoading = true; // Start loading
    });

    try {
      final response = await dioConsumer.get(
        '/api/items/search',
        queryParameters: {'keyword': query},
      );

      if (response['data'] != null) {
        final List<Map<String, dynamic>> results =
        List<Map<String, dynamic>>.from(response['data']);
        setState(() {
          _searchResults = results; // Update search results
        });
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('No data found', style: AppTextStyles.font14Regular.copyWith(color: Colors.white))),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e', style: AppTextStyles.font14Regular.copyWith(color: Colors.white))),
      );
    } finally {
      setState(() {
        _isLoading = false; // Stop loading
      });
    }
  }

  // Perform search when the user submits the query
  void _performSearch() {
    final String query = _searchController.text.trim();

    if (query.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('أدخل كلمة البحث...', style: AppTextStyles.font14Regular.copyWith(color: Colors.white))),
      );
      return;
    }

    _fetchSearchResults(query); // Fetch results from the API
  }

  // Navigate to ReelsPage with search results and query
  void _navigateToReelsPage() {
    if (_searchResults.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('لا توجد نتائج بحث', style: AppTextStyles.font14Regular.copyWith(color: Colors.white))),
      );
      return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ReelsPage(
          searchResults: _searchResults,
          searchQuery: _searchController.text.trim(),
          serviceCategoryId: 0,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('بحث', style: AppTextStyles.font18Bold),
        backgroundColor: AppColors.black,
      ),
      body: Column(
        children: [
          // Search Bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppColors.lightGrey10,
                borderRadius: BorderRadius.circular(19),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'حياك ... دور علي اللي تبيه',
                        hintStyle: AppTextStyles.font14Medium.copyWith(
                          color: AppColors.white,
                        ),
                        border: InputBorder.none,
                      ),
                      style: AppTextStyles.font14Medium.copyWith(
                        color: AppColors.white,
                      ),
                      onSubmitted: (_) =>
                          _performSearch(), // Trigger search on Enter
                    ),
                  ),
                  _isLoading
                      ? const CircularProgressIndicator(
                    color: AppColors.white,
                  )
                      : IconButton(
                    icon: const Icon(Icons.search,
                        color: AppColors.white, size: 28),
                    onPressed: () {
                      _performSearch(); // Perform search
                      _navigateToReelsPage(); // Navigate to ReelsPage
                    },
                  ),
                ],
              ),
            ),
          ),

          // Search Results
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _searchResults.isEmpty
                ? const Center(
              child: Text(
                'لا توجد نتائج بحث',
                style: AppTextStyles.font16Medium,
              ),
            )
                : ListView.builder(
              itemCount: _searchResults.length,
              itemBuilder: (context, index) {
                final item = _searchResults[index];
                return ListTile(
                  title: Text(
                    item['title'] ?? 'No Title',
                    style: AppTextStyles.font16Bold,
                  ),
                  subtitle: Text(
                    item['content'] ?? 'No Description',
                    style: AppTextStyles.font14Medium,
                  ),
                  leading: item['image'] != null
                      ? Image.network(item['image'])
                      : const Icon(Icons.search),
                  onTap: _navigateToReelsPage, // Navigate on tap
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}