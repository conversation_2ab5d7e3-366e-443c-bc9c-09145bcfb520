import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/utils/app_assets.dart';
import 'package:gather_point/feature/reservations/presentation/views/reservation_confirmation_page.dart';
import 'package:gather_point/feature/reservations/presentation/widget/calendar_widget.dart';
import 'package:intl/intl.dart';
import 'package:video_player/video_player.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';

import 'facility_icon_widget.dart';

class VideoPlayerWidget extends StatefulWidget {
  final List<dynamic> facilities;
  final bool favorite;
  final String videoUrl;
  final String title;
  final String location;
  final num price;
  final int serviceCategoryId;
  final int id;
  final DioConsumer dioConsumer;

  const VideoPlayerWidget({
    super.key,
    required this.facilities,
    required this.videoUrl,
    required this.title,
    required this.location,
    required this.price,
    required this.serviceCategoryId,
    required this.id,
    required this.dioConsumer,
    required this.favorite,
  });

  @override
  _VideoPlayerWidgetState createState() => _VideoPlayerWidgetState();
}

class _VideoPlayerWidgetState extends State<VideoPlayerWidget> {
  late VideoPlayerController _controller;
  bool isLiked = false;
  bool _isLoading = true;
  bool _hasError = false;
  bool _isMuted = true;
  final AudioPlayer _audioPlayer = AudioPlayer();

  @override
  void initState() {
    super.initState();
    isLiked = widget.favorite;
    _initializeVideo();
  }

  @override
  void dispose() {
    _controller.dispose();
    _audioPlayer.dispose();
    super.dispose();
  }

  Future<void> _initializeVideo() async {
    try {
      final file = await DefaultCacheManager().getSingleFile(widget.videoUrl);
      _controller = VideoPlayerController.file(file)
        ..initialize().then((_) {
          if (mounted) {
            setState(() => _isLoading = false);
            _controller.setVolume(_isMuted ? 0.0 : 1.0);
            _controller.play();
            _controller.setLooping(true);
          }
        });
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
        });
      }
    }
  }

  void _toggleMute() {
    setState(() {
      _isMuted = !_isMuted;
      _controller.setVolume(_isMuted ? 0.0 : 1.0);
    });
  }

  void _showReservationDialog(BuildContext context) {
    DateTime? startDate;
    DateTime? endDate;

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text("اختيار تاريخ الحجز"),
              content: SizedBox(
                width: 500,
                height: 400,
                child: CalendarWidget(onDateSelected:
                    (DateTime? start, DateTime? end, double totalPrice) {
                  startDate = start;
                  endDate = end;
                }),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text("إلغاء"),
                ),
                ElevatedButton(
                  onPressed: () async {
                    if (startDate != null && endDate != null) {
                      await _getReservationInfo(context, startDate!, endDate!);
                    } else {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                            content: Text("يرجى اختيار كلا التاريخين")),
                      );
                    }
                  },
                  child: const Text("مراجعة الحجز"),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _getReservationInfo(
      BuildContext context, DateTime startDate, DateTime endDate) async {
    try {
      final response = await widget.dioConsumer.post(
        '/api/reservations/check',
        data: {
          'service_category_item_id': widget.id,
          'reservation_from':
              DateFormat('yyyy-MM-dd HH:mm:ss', 'en-US').format(startDate),
          'reservation_to':
              DateFormat('yyyy-MM-dd HH:mm:ss', 'en-US').format(endDate),
        },
      );

      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ReservationConfirmationPage(
            reservationData: response['data'],
            dioConsumer: widget.dioConsumer,
            videoId: widget.id,
            reservationFrom:
                DateFormat('yyyy-MM-dd HH:mm:ss', 'en-US').format(startDate),
            reservationTo:
                DateFormat('yyyy-MM-dd HH:mm:ss', 'en-US').format(endDate),
          ),
        ),
      );
    } on DioException catch (e) {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text("خطأ"),
            content: Text(e.response?.statusCode == 409
                ? "الفترة المحددة غير متاحة، يرجى اختيار فترة أخرى."
                : "حدث خطأ أثناء جلب المعلومات: ${e.message}"),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text("حسنًا"),
              ),
            ],
          );
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          if (_isLoading)
            const Center(child: CircularProgressIndicator())
          else if (_hasError)
            const Center(
                child: Text("Failed to load video",
                    style: TextStyle(color: Colors.white)))
          else
            GestureDetector(
              onTap: () {
                setState(() {
                  _controller.value.isPlaying
                      ? _controller.pause()
                      : _controller.play();
                });
              },
              child: SizedBox.expand(
                child: FittedBox(
                  fit: BoxFit.cover,
                  child: SizedBox(
                    width: _controller.value.size.width,
                    height: _controller.value.size.height,
                    child: VideoPlayer(_controller),
                  ),
                ),
              ),
            ),

          // Back Button
          widget.serviceCategoryId == 0
              ? (Positioned(
                  top: MediaQuery.of(context).padding.top + 20,
                  left: 10,
                  right: 10,
                  child: Row(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.arrow_back,
                            color: Colors.white, size: 30),
                        onPressed: () => Navigator.of(context).pop(),
                      ),
                    ],
                  ),
                ))
              : const SizedBox(height: 0),
          Positioned(
            top: 40,
            left: 0,
            right: 0,
            child: Center(
              child: Column(
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Image.asset(
                        AppAssets.imagesLogoCircle,
                        width: 50,
                        height: 50,
                      ),
                      const SizedBox(width: 8),
                      const Text(
                        "GATHER\nPOINT",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  IconButton(
                    icon: Icon(
                      _isMuted ? Icons.volume_off : Icons.volume_up,
                      color: Colors.white,
                      size: 30,
                    ),
                    onPressed: _toggleMute,
                  ),
                ],
              ),
            ),
          ),
          Positioned(
            left: 20,
            bottom: 160,
            child: Column(
              children: [
                IconButton(
                  icon: Icon(isLiked ? Icons.favorite : Icons.favorite_border,
                      size: 40, color: isLiked ? Colors.red : Colors.white),
                  onPressed: () async {
                    final current = isLiked;
                    setState(() => isLiked = !isLiked);
                    try {
                      await widget.dioConsumer.post(
                        '/api/favorite/set',
                        data: {'service_category_item_id': widget.id},
                      );
                    } catch (_) {
                      setState(() => isLiked = current);
                    }
                  },
                ),
                const SizedBox(height: 20),
                const Icon(Icons.comment, color: Colors.white, size: 40),
                const SizedBox(height: 20),
                const Icon(Icons.share, color: Colors.white, size: 40),
              ],
            ),
          ),
          Positioned(
            bottom: 160,
            right: 20,
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.4), // خلفية شفافة
                borderRadius: BorderRadius.circular(12),
              ),
              child: Builder(builder: (context) {
                final filteredFacilities = widget.facilities
                    .where((facility) => (facility['count'] ?? 0) > 0)
                    .toList();

                return Column(
                  children: [
                    if (filteredFacilities.isNotEmpty)
                      Row(
                        children: filteredFacilities.take(2).map((facility) {
                          return Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 10),
                            child: FacilityIcon(
                              iconUrl: facility['icon'],
                              title: '${facility['count']}',
                              iconSize: 60,
                            ),
                          );
                        }).toList(),
                      ),
                    if (filteredFacilities.length > 2)
                      const SizedBox(height: 20),
                    if (filteredFacilities.length > 2)
                      Row(
                        children:
                            filteredFacilities.skip(2).take(2).map((facility) {
                          return Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 10),
                            child: FacilityIcon(
                              iconUrl: facility['icon'],
                              title:
                                  '${facility['count']} ${facility['title']}',
                              iconSize: 30,
                            ),
                          );
                        }).toList(),
                      ),
                  ],
                );
              }),
            ),
          ),
          Positioned(
            bottom: 80,
            left: 20,
            right: 20,
            child: ElevatedButton(
              onPressed: () => _showReservationDialog(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.yellow,
                padding: const EdgeInsets.symmetric(vertical: 14),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text(
                "أحجز الآن",
                style: TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
          ),
          if (!_isLoading && !_hasError)
            Positioned(
              bottom: 65,
              left: 0,
              right: 0,
              child: VideoProgressIndicator(
                _controller,
                allowScrubbing: true,
                colors: const VideoProgressColors(
                  playedColor: AppColors.yellow,
                  bufferedColor: Colors.white54,
                  backgroundColor: Colors.black26,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class ServiceCategory {
  final String title;
  final String icon;

  ServiceCategory({required this.title, required this.icon});

  factory ServiceCategory.fromJson(Map<String, dynamic> json) {
    return ServiceCategory(
      title: json['title'],
      icon: json['icon'],
    );
  }
}
