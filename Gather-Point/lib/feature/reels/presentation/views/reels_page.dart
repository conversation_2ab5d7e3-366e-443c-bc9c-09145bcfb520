import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/reels/presentation/views/widgets/video_player_widget.dart';
import 'package:hive/hive.dart';

class ReelsPage extends StatefulWidget {
  final List<Map<String, dynamic>> searchResults;
  final String searchQuery;
  final int serviceCategoryId;

  const ReelsPage({
    super.key,
    required this.searchResults,
    required this.searchQuery,
    required this.serviceCategoryId,
  });

  @override
  _ReelsPageState createState() => _ReelsPageState();
}

class _ReelsPageState extends State<ReelsPage> {
  List<Map<String, dynamic>> videoData = [];
  bool isLoading = true;
  bool hasError = false;
  late final DioConsumer dioConsumer;

  @override
  void initState() {
    super.initState();
    dioConsumer = DioConsumer(
      dio: getIt<Dio>(),
      profileBox: getIt<Box<UserEntity>>(),
    );
    if (widget.searchResults.isNotEmpty) {
      setState(() {
        videoData = widget.searchResults;
        isLoading = false;
        hasError = false;
      });
    } else {
      fetchReels();
    }
  }

  Future<void> fetchReels() async {
    try {
      setState(() {
        isLoading = true;
        hasError = false;
      });

      final response = await dioConsumer.get(
        '/api/items/list',
        queryParameters: {
          'service_category_id': widget.serviceCategoryId,
          'reels': 1
        },
      );

      if (response['data'] != null) {
        setState(() {
          videoData = List<Map<String, dynamic>>.from(response['data']);
          isLoading = false;
          hasError = false;
        });
      } else {
        setState(() {
          isLoading = false;
          hasError = true;
        });
      }
    } catch (e) {
      print("Error fetching reels: $e");
      setState(() {
        isLoading = false;
        hasError = true;
      });
    }
  }

  Widget _buildLoading() {
    return const Center(child: CircularProgressIndicator());
  }

  Widget _buildError() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error, color: Colors.white, size: 50),
          const SizedBox(height: 10),
          Text(
            "Failed to load reels or no data available",
            style: AppTextStyles.font16Regular.copyWith(color: Colors.white),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 10),
          ElevatedButton(
            onPressed: fetchReels,
            child: Text("Retry", style: AppTextStyles.font16Medium.copyWith(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  Widget _buildNoResults() {
    return Center(
      child: Text(
        "No search results found for '\${widget.searchQuery}'",
        style: AppTextStyles.font16Regular.copyWith(color: Colors.white),
      ),
    );
  }

  Widget _buildReels() {
    return PageView.builder(
      scrollDirection: Axis.vertical,
      itemCount: videoData.length,
      itemBuilder: (context, index) {
        final item = videoData[index];
        return VideoPlayerWidget(
          facilities: item['facilities'] ?? [],
          videoUrl: item['video'],
          title: item['title'],
          location: item['title'],
          id: item['id'],
          price: (item['price'] as num).toDouble(),
          serviceCategoryId: widget.serviceCategoryId,
          favorite: item['favorite'],
          dioConsumer: dioConsumer,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          if (isLoading)
            _buildLoading()
          else if (hasError)
            _buildError()
          else if (videoData.isEmpty)
            _buildNoResults()
          else
            _buildReels()
        ],
      ),
    );
  }
}
