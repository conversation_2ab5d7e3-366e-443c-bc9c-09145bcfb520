import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'reserve_screen.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/typography_helper.dart';

class PlaceDetailsScreen extends StatefulWidget {
  final Map<String, dynamic> placeData;

  const PlaceDetailsScreen({super.key, required this.placeData});

  @override
  State<PlaceDetailsScreen> createState() => _PlaceDetailsScreenState();
}

class _PlaceDetailsScreenState extends State<PlaceDetailsScreen> {
  bool isFavorite = false;
  bool showFullDescription = false;

  String getValidImageUrl(String? url) {
    if (url == null || url.isEmpty || url == 'null') {
      return 'https://placehold.co/400x300';
    }
    return url;
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    return Scaffold(
      backgroundColor: Colors.white,
      body: CustomScrollView(
        slivers: [
          // Hero Image Section with App Bar
          SliverAppBar(
            expandedHeight: 300,
            pinned: true,
            backgroundColor: Colors.white,
            elevation: 0,
            systemOverlayStyle: SystemUiOverlayStyle.dark,
            leading: Container(
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.9),
                shape: BoxShape.circle,
              ),
              child: IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.black),
                onPressed: () => Navigator.pop(context),
              ),
            ),
            actions: [
              Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.9),
                  shape: BoxShape.circle,
                ),
                child: IconButton(
                  icon: const Icon(Icons.share, color: Colors.black),
                  onPressed: () {},
                ),
              ),
              Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.9),
                  shape: BoxShape.circle,
                ),
                child: IconButton(
                  icon: Icon(
                    isFavorite ? Icons.favorite : Icons.favorite_border,
                    color: isFavorite ? Colors.red : Colors.black,
                  ),
                  onPressed: () {
                    setState(() {
                      isFavorite = !isFavorite;
                    });
                  },
                ),
              ),
            ],
            flexibleSpace: FlexibleSpaceBar(
              background: Image.network(
                getValidImageUrl(widget.placeData['image']),
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Image.network(
                  'https://placehold.co/400x300',
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),

          // Content
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title and Location
                  _buildTitleSection(s),
                  const SizedBox(height: 20),

                  // Quick Stats
                  _buildQuickStats(s),
                  const SizedBox(height: 20),

                  const Divider(),
                  const SizedBox(height: 20),

                  // Host Information
                  _buildHostSection(s),
                  const SizedBox(height: 20),

                  const Divider(),
                  const SizedBox(height: 20),

                  // Description
                  _buildDescriptionSection(s),
                  const SizedBox(height: 20),

                  const Divider(),
                  const SizedBox(height: 20),

                  // Amenities
                  _buildAmenitiesSection(s),
                  const SizedBox(height: 20),

                  const Divider(),
                  const SizedBox(height: 20),

                  // Location
                  _buildLocationSection(s),
                  const SizedBox(height: 20),

                  const Divider(),
                  const SizedBox(height: 20),

                  // Reviews
                  _buildReviewsSection(s),
                  const SizedBox(height: 100), // Space for bottom booking bar
                ],
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: _buildBookingBottomBar(s),
    );
  }

  Widget _buildTitleSection(S s) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TypographyHelper.smartText(
          widget.placeData['title'] ?? 'No Title',
          isLargeHeading: true,
          color: AppColors.black,
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            const Icon(Icons.location_on, size: 16, color: AppColors.darkGrey3),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                "${widget.placeData['city'] ?? ''} - ${widget.placeData['country'] ?? ''}",
                style: AppTextStyles.font16Regular.copyWith(
                  color: AppColors.darkGrey3,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            const Icon(Icons.star, size: 18, color: Colors.amber),
            const SizedBox(width: 4),
            Text(
              '${widget.placeData['reviews'] ?? 0}',
              style: AppTextStyles.font16SemiBold.copyWith(
                color: AppColors.black,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              '(${(widget.placeData['reviews'] ?? 0) * 12} reviews)',
              style: AppTextStyles.font14Regular.copyWith(
                color: AppColors.darkGrey3,
                decoration: TextDecoration.underline,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickStats(S s) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem(Icons.king_bed, '${widget.placeData["bedrooms"] ?? 0}', 'Bedrooms'),
          _buildStatItem(Icons.bathtub, '${widget.placeData["baths"] ?? 0}', 'Bathrooms'),
          _buildStatItem(Icons.people, '${widget.placeData["no_guests"] ?? 0}', 'Guests'),
          if (widget.placeData['wifi'] == true)
            _buildStatItem(Icons.wifi, '', 'WiFi'),
        ],
      ),
    );
  }

  Widget _buildStatItem(IconData icon, String value, String label) {
    return Column(
      children: [
        Icon(icon, size: 24, color: Colors.grey[700]),
        const SizedBox(height: 8),
        if (value.isNotEmpty)
          Text(
            value,
            style: AppTextStyles.font16Bold.copyWith(
              color: AppColors.black,
            ),
          ),
        Text(
          label,
          style: AppTextStyles.font12Medium.copyWith(
            color: AppColors.darkGrey3,
          ),
        ),
      ],
    );
  }

  Widget _buildHostSection(S s) {
    return Row(
      children: [
        CircleAvatar(
          radius: 25,
          backgroundColor: Colors.grey[300],
          child: const Icon(Icons.person, color: Colors.grey),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Hosted by Property Owner',
                style: AppTextStyles.font18Bold.copyWith(
                  color: AppColors.black,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Superhost · 2 years hosting',
                style: AppTextStyles.font14Regular.copyWith(
                  color: AppColors.darkGrey3,
                ),
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.amber[100],
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            'Superhost',
            style: AppTextStyles.font12SemiBold.copyWith(
              color: AppColors.black,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDescriptionSection(S s) {
    final description = widget.placeData['description'] ?? 'No description available.';
    final shouldShowMore = description.length > 200;
    final displayText = showFullDescription || !shouldShowMore
        ? description
        : '${description.substring(0, 200)}...';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'About this place',
          style: AppTextStyles.font20Bold.copyWith(
            color: AppColors.black,
          ),
        ),
        const SizedBox(height: 12),
        TypographyHelper.smartText(
          displayText,
          color: AppColors.darkGrey2,
        ),
        if (shouldShowMore) ...[
          const SizedBox(height: 8),
          GestureDetector(
            onTap: () {
              setState(() {
                showFullDescription = !showFullDescription;
              });
            },
            child: Text(
              showFullDescription ? 'Show less' : 'Show more',
              style: AppTextStyles.font16SemiBold.copyWith(
                color: AppColors.black,
                decoration: TextDecoration.underline,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildAmenitiesSection(S s) {
    final features = widget.placeData['features'] as List<dynamic>? ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'What this place offers',
          style: AppTextStyles.font20Bold.copyWith(
            color: AppColors.black,
          ),
        ),
        const SizedBox(height: 16),
        if (features.isEmpty)
          Text(
            'No amenities listed',
            style: AppTextStyles.font16Regular.copyWith(
              color: AppColors.darkGrey3,
            ),
          )
        else
          ...features.take(6).map((feature) => Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: Row(
              children: [
                Icon(
                  feature['icon'] ?? Icons.check,
                  size: 20,
                  color: Colors.green,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    feature['label'] ?? 'Feature',
                    style: AppTextStyles.font16Regular.copyWith(
                      color: AppColors.black,
                    ),
                  ),
                ),
              ],
            ),
          )),
        if (features.length > 6) ...[
          const SizedBox(height: 8),
          OutlinedButton(
            onPressed: () {
              // Show all amenities dialog
            },
            style: OutlinedButton.styleFrom(
              side: const BorderSide(color: Colors.black),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Show all ${features.length} amenities',
              style: AppTextStyles.font16Medium.copyWith(
                color: AppColors.black,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildLocationSection(S s) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Where you\'ll be',
          style: AppTextStyles.font20Bold.copyWith(
            color: AppColors.black,
          ),
        ),
        const SizedBox(height: 12),
        Text(
          "${widget.placeData['city'] ?? ''}, ${widget.placeData['country'] ?? ''}",
          style: AppTextStyles.font16Regular.copyWith(
            color: AppColors.darkGrey2,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          height: 200,
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(12),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.map, size: 48, color: AppColors.darkGrey3),
                const SizedBox(height: 8),
                Text(
                  'Map view',
                  style: AppTextStyles.font16Regular.copyWith(
                    color: AppColors.darkGrey3,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildReviewsSection(S s) {
    final rating = widget.placeData['reviews'] ?? 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.star, size: 24, color: Colors.amber),
            const SizedBox(width: 8),
            Text(
              '$rating',
              style: AppTextStyles.font20Bold.copyWith(
                color: AppColors.black,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              '(${rating * 12} reviews)',
              style: AppTextStyles.font16Regular.copyWith(
                color: AppColors.darkGrey3,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        // Sample review
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    radius: 20,
                    backgroundColor: Colors.grey[300],
                    child: const Icon(Icons.person, size: 20, color: Colors.grey),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Guest Review',
                          style: AppTextStyles.font16SemiBold.copyWith(
                            color: AppColors.black,
                          ),
                        ),
                        Text(
                          'March 2024',
                          style: AppTextStyles.font14Regular.copyWith(
                            color: AppColors.darkGrey3,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Row(
                    children: List.generate(5, (index) => Icon(
                      index < rating ? Icons.star : Icons.star_border,
                      size: 16,
                      color: Colors.amber,
                    )),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                'Great place to stay! Clean, comfortable, and exactly as described. The host was very responsive and helpful.',
                style: AppTextStyles.font14Regular.copyWith(
                  color: AppColors.darkGrey2,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        OutlinedButton(
          onPressed: () {
            // Show all reviews
          },
          style: OutlinedButton.styleFrom(
            side: const BorderSide(color: Colors.black),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(
            'Show all ${rating * 12} reviews',
            style: AppTextStyles.font16Medium.copyWith(
              color: AppColors.black,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBookingBottomBar(S s) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  RichText(
                    text: TextSpan(
                      text: '\$${widget.placeData['price'] ?? '0'} ',
                      style: AppTextStyles.font20Bold.copyWith(
                        color: AppColors.black,
                      ),
                      children: [
                        TextSpan(
                          text: 'night',
                          style: AppTextStyles.font16Regular.copyWith(
                            color: AppColors.darkGrey3,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(Icons.star, size: 16, color: Colors.amber),
                      const SizedBox(width: 4),
                      Text(
                        '${widget.placeData['reviews'] ?? 0}',
                        style: AppTextStyles.font14SemiBold.copyWith(
                          color: AppColors.black,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red[400],
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ReserveScreen(
                        placeTitle: widget.placeData["title"],
                        policy: widget.placeData["policy"],
                        pricePerNight: double.parse(
                            widget.placeData["price"].toString().replaceAll('€', '').replaceAll('\$', '')),
                      ),
                    ),
                  );
                },
                child: Text(
                  'Reserve',
                  style: AppTextStyles.font16SemiBold.copyWith(
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// ✅ Widget مخصص للأيقونة والنص
class FeatureIcon extends StatelessWidget {
  final IconData icon;
  final String label;

  const FeatureIcon({super.key, required this.icon, required this.label});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Icon(icon, size: 30),
        const SizedBox(height: 8),
        Text(label, style: AppTextStyles.font12Regular.copyWith(
          color: AppColors.darkGrey3,
        )),
      ],
    );
  }
}
