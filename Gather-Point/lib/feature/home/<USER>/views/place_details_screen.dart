import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'reserve_screen.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/styles/typography_helper.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';

class PlaceDetailsScreen extends StatefulWidget {
  final Map<String, dynamic> placeData;

  const PlaceDetailsScreen({super.key, required this.placeData});

  @override
  State<PlaceDetailsScreen> createState() => _PlaceDetailsScreenState();
}

class _PlaceDetailsScreenState extends State<PlaceDetailsScreen> {
  bool isFavorite = false;
  bool showFullDescription = false;

  String getValidImageUrl(String? url) {
    if (url == null || url.isEmpty || url == 'null') {
      return 'https://placehold.co/400x300';
    }
    return url;
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    return Scaffold(
      backgroundColor: context.backgroundColor,
      body: CustomScrollView(
        slivers: [
          // Enhanced Hero Image Section with App Bar
          SliverAppBar(
            expandedHeight: 300,
            pinned: true,
            backgroundColor: context.backgroundColor,
            elevation: 0,
            systemOverlayStyle: context.isDarkMode
                ? SystemUiOverlayStyle.light
                : SystemUiOverlayStyle.dark,
            leading: Container(
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: context.cardColor.withValues(alpha: 0.9),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: IconButton(
                icon: Icon(
                  Icons.arrow_back_ios_rounded,
                  color: context.primaryTextColor,
                ),
                onPressed: () => Navigator.pop(context),
              ),
            ),
            actions: [
              Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: context.cardColor.withValues(alpha: 0.9),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: IconButton(
                  icon: Icon(
                    Icons.share_rounded,
                    color: context.primaryTextColor,
                  ),
                  onPressed: () {},
                ),
              ),
              Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: context.cardColor.withValues(alpha: 0.9),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: IconButton(
                  icon: Icon(
                    isFavorite ? Icons.favorite_rounded : Icons.favorite_border_rounded,
                    color: isFavorite ? Colors.red : context.primaryTextColor,
                  ),
                  onPressed: () {
                    setState(() {
                      isFavorite = !isFavorite;
                    });
                  },
                ),
              ),
            ],
            flexibleSpace: FlexibleSpaceBar(
              background: Stack(
                fit: StackFit.expand,
                children: [
                  Image.network(
                    getValidImageUrl(widget.placeData['image']),
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      color: context.secondaryTextColor.withValues(alpha: 0.1),
                      child: Icon(
                        Icons.image_not_supported_rounded,
                        size: 64,
                        color: context.secondaryTextColor,
                      ),
                    ),
                  ),
                  // Gradient overlay for better text readability
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withValues(alpha: 0.3),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Enhanced Content
          SliverToBoxAdapter(
            child: Container(
              color: context.backgroundColor,
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Enhanced Title and Location
                    _buildTitleSection(s),
                    const SizedBox(height: 24),

                    // Enhanced Quick Stats
                    _buildQuickStats(s),
                    const SizedBox(height: 24),

                    // Enhanced Host Information
                    _buildHostSection(s),
                    const SizedBox(height: 24),

                    // Enhanced Description
                    _buildDescriptionSection(s),
                    const SizedBox(height: 24),

                    // Enhanced Amenities
                    _buildAmenitiesSection(s),
                    const SizedBox(height: 24),

                    // Enhanced Location
                    _buildLocationSection(s),
                    const SizedBox(height: 24),

                    // Enhanced Reviews
                    _buildReviewsSection(s),
                    const SizedBox(height: 100), // Space for bottom booking bar
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: _buildBookingBottomBar(s),
    );
  }

  Widget _buildTitleSection(S s) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.placeData['title'] ?? 'لا يوجد عنوان',
            style: AppTextStyles.font24Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Icon(
                Icons.location_on_rounded,
                size: 18,
                color: context.accentColor,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  "${widget.placeData['city'] ?? ''} - ${widget.placeData['country'] ?? ''}",
                  style: AppTextStyles.font16Regular.copyWith(
                    color: context.secondaryTextColor,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.amber.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.star_rounded, size: 16, color: Colors.amber),
                    const SizedBox(width: 4),
                    Text(
                      '${widget.placeData['reviews'] ?? 0}',
                      style: AppTextStyles.font14SemiBold.copyWith(
                        color: Colors.amber[800],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 12),
              Text(
                '(${(widget.placeData['reviews'] ?? 0) * 12} تقييم)',
                style: AppTextStyles.font14Regular.copyWith(
                  color: context.secondaryTextColor,
                  decoration: TextDecoration.underline,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: context.accentColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '${widget.placeData['price'] ?? 0} ر.س/ليلة',
                  style: AppTextStyles.font16Bold.copyWith(
                    color: context.accentColor,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats(S s) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'تفاصيل العقار',
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem(Icons.bed_rounded, '${widget.placeData["bedrooms"] ?? 0}', 'غرف النوم'),
              _buildStatItem(Icons.bathtub_rounded, '${widget.placeData["baths"] ?? 0}', 'الحمامات'),
              _buildStatItem(Icons.people_rounded, '${widget.placeData["no_guests"] ?? 0}', 'الضيوف'),
              if (widget.placeData['wifi'] == true)
                _buildStatItem(Icons.wifi_rounded, '', 'واي فاي'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(IconData icon, String value, String label) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: context.accentColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            size: 24,
            color: context.accentColor,
          ),
        ),
        const SizedBox(height: 8),
        if (value.isNotEmpty)
          Text(
            value,
            style: AppTextStyles.font16Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
        Text(
          label,
          style: AppTextStyles.font12Regular.copyWith(
            color: context.secondaryTextColor,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildHostSection(S s) {
    return Row(
      children: [
        CircleAvatar(
          radius: 25,
          backgroundColor: Colors.grey[300],
          child: const Icon(Icons.person, color: Colors.grey),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Hosted by Property Owner',
                style: AppTextStyles.font18Bold.copyWith(
                  color: AppColors.black,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Superhost · 2 years hosting',
                style: AppTextStyles.font14Regular.copyWith(
                  color: AppColors.darkGrey3,
                ),
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.amber[100],
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            'Superhost',
            style: AppTextStyles.font12SemiBold.copyWith(
              color: AppColors.black,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDescriptionSection(S s) {
    final description = widget.placeData['description'] ?? 'No description available.';
    final shouldShowMore = description.length > 200;
    final displayText = showFullDescription || !shouldShowMore
        ? description
        : '${description.substring(0, 200)}...';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'About this place',
          style: AppTextStyles.font20Bold.copyWith(
            color: AppColors.black,
          ),
        ),
        const SizedBox(height: 12),
        TypographyHelper.smartText(
          displayText,
          color: AppColors.darkGrey2,
        ),
        if (shouldShowMore) ...[
          const SizedBox(height: 8),
          GestureDetector(
            onTap: () {
              setState(() {
                showFullDescription = !showFullDescription;
              });
            },
            child: Text(
              showFullDescription ? 'Show less' : 'Show more',
              style: AppTextStyles.font16SemiBold.copyWith(
                color: AppColors.black,
                decoration: TextDecoration.underline,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildAmenitiesSection(S s) {
    final features = widget.placeData['features'] as List<dynamic>? ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'What this place offers',
          style: AppTextStyles.font20Bold.copyWith(
            color: AppColors.black,
          ),
        ),
        const SizedBox(height: 16),
        if (features.isEmpty)
          Text(
            'No amenities listed',
            style: AppTextStyles.font16Regular.copyWith(
              color: AppColors.darkGrey3,
            ),
          )
        else
          ...features.take(6).map((feature) => Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: Row(
              children: [
                Icon(
                  feature['icon'] ?? Icons.check,
                  size: 20,
                  color: Colors.green,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    feature['label'] ?? 'Feature',
                    style: AppTextStyles.font16Regular.copyWith(
                      color: AppColors.black,
                    ),
                  ),
                ),
              ],
            ),
          )),
        if (features.length > 6) ...[
          const SizedBox(height: 8),
          OutlinedButton(
            onPressed: () {
              // Show all amenities dialog
            },
            style: OutlinedButton.styleFrom(
              side: const BorderSide(color: Colors.black),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Show all ${features.length} amenities',
              style: AppTextStyles.font16Medium.copyWith(
                color: AppColors.black,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildLocationSection(S s) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Where you\'ll be',
          style: AppTextStyles.font20Bold.copyWith(
            color: AppColors.black,
          ),
        ),
        const SizedBox(height: 12),
        Text(
          "${widget.placeData['city'] ?? ''}, ${widget.placeData['country'] ?? ''}",
          style: AppTextStyles.font16Regular.copyWith(
            color: AppColors.darkGrey2,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          height: 200,
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(12),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.map, size: 48, color: AppColors.darkGrey3),
                const SizedBox(height: 8),
                Text(
                  'Map view',
                  style: AppTextStyles.font16Regular.copyWith(
                    color: AppColors.darkGrey3,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildReviewsSection(S s) {
    final rating = widget.placeData['reviews'] ?? 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.star, size: 24, color: Colors.amber),
            const SizedBox(width: 8),
            Text(
              '$rating',
              style: AppTextStyles.font20Bold.copyWith(
                color: AppColors.black,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              '(${rating * 12} reviews)',
              style: AppTextStyles.font16Regular.copyWith(
                color: AppColors.darkGrey3,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        // Sample review
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    radius: 20,
                    backgroundColor: Colors.grey[300],
                    child: const Icon(Icons.person, size: 20, color: Colors.grey),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Guest Review',
                          style: AppTextStyles.font16SemiBold.copyWith(
                            color: AppColors.black,
                          ),
                        ),
                        Text(
                          'March 2024',
                          style: AppTextStyles.font14Regular.copyWith(
                            color: AppColors.darkGrey3,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Row(
                    children: List.generate(5, (index) => Icon(
                      index < rating ? Icons.star : Icons.star_border,
                      size: 16,
                      color: Colors.amber,
                    )),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                'Great place to stay! Clean, comfortable, and exactly as described. The host was very responsive and helpful.',
                style: AppTextStyles.font14Regular.copyWith(
                  color: AppColors.darkGrey2,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        OutlinedButton(
          onPressed: () {
            // Show all reviews
          },
          style: OutlinedButton.styleFrom(
            side: const BorderSide(color: Colors.black),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(
            'Show all ${rating * 12} reviews',
            style: AppTextStyles.font16Medium.copyWith(
              color: AppColors.black,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBookingBottomBar(S s) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  RichText(
                    text: TextSpan(
                      text: '\$${widget.placeData['price'] ?? '0'} ',
                      style: AppTextStyles.font20Bold.copyWith(
                        color: AppColors.black,
                      ),
                      children: [
                        TextSpan(
                          text: 'night',
                          style: AppTextStyles.font16Regular.copyWith(
                            color: AppColors.darkGrey3,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(Icons.star, size: 16, color: Colors.amber),
                      const SizedBox(width: 4),
                      Text(
                        '${widget.placeData['reviews'] ?? 0}',
                        style: AppTextStyles.font14SemiBold.copyWith(
                          color: AppColors.black,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red[400],
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ReserveScreen(
                        placeTitle: widget.placeData["title"],
                        policy: widget.placeData["policy"],
                        pricePerNight: double.parse(
                            widget.placeData["price"].toString().replaceAll('€', '').replaceAll('\$', '')),
                      ),
                    ),
                  );
                },
                child: Text(
                  'Reserve',
                  style: AppTextStyles.font16SemiBold.copyWith(
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// ✅ Widget مخصص للأيقونة والنص
class FeatureIcon extends StatelessWidget {
  final IconData icon;
  final String label;

  const FeatureIcon({super.key, required this.icon, required this.label});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Icon(icon, size: 30),
        const SizedBox(height: 8),
        Text(label, style: AppTextStyles.font12Regular.copyWith(
          color: AppColors.darkGrey3,
        )),
      ],
    );
  }
}
