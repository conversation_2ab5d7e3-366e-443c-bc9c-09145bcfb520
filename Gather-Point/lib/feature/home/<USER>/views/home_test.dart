import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/utils/app_assets.dart';
import 'package:gather_point/core/services/location_service.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/City.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/ServiceCategory.dart';
import 'package:gather_point/feature/reels/presentation/views/reels_page.dart';
import 'package:gather_point/feature/settings/presentation/views/settings_view.dart';
import 'package:geolocator/geolocator.dart';
import 'package:hive/hive.dart';

import 'explore_list_view.dart';

class GatherPointHome extends StatefulWidget {
  const GatherPointHome({super.key});

  @override
  State<GatherPointHome> createState() => _GatherPointHomeState();
}

class _GatherPointHomeState extends State<GatherPointHome> {
  int? _selectedCategoryId;
  City? _currentCity;
  List<City> _cities = [];
  List<ServiceCategory> _categories = [];
  List<ServiceCategory> _categoryItems = [];
  List<Map<String, dynamic>> _reelItems = [];
  late final DioConsumer _dioConsumer;

  @override
  void initState() {
    super.initState();
    _dioConsumer = DioConsumer(
      dio: getIt<Dio>(),
      profileBox: getIt<Box<UserEntity>>(),
    );
    _loadCitiesAndSelectClosest();
    _loadCategories();
  }

  Future<void> _loadCitiesAndSelectClosest() async {
    final locationService = LocationService();
    try {
      final hasPermission = await locationService.checkLocationPermissions();
      if (!hasPermission) {
        throw Exception('Location permission denied');
      }
      final position = await Geolocator.getCurrentPosition();
      final cities = await locationService.getCitiesAndClosest(position);

      setState(() {
        _currentCity = cities['closestCity'];
        _cities = cities['allCities'];
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              S.of(context).locationPermissionError,
              style: AppTextStyles.font14Regular.copyWith(color: Colors.white),
            ),
          ),
        );
      }
    }
  }

  void _showCityDialog() {
    showDialog(
      context: context,
      builder: (dialogContext) {
        return AlertDialog(
          backgroundColor: dialogContext.cardColor,
          title: Text('اختر المدينة',
              style: AppTextStyles.font18Bold
                  .copyWith(color: dialogContext.primaryTextColor)),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: _cities.length,
              itemBuilder: (context, index) {
                final city = _cities[index];
                return ListTile(
                  title: Text(city.name,
                      style: AppTextStyles.font16Regular
                          .copyWith(color: dialogContext.primaryTextColor)),
                  hoverColor: dialogContext.accentColor.withValues(alpha: 0.1),
                  onTap: () {
                    setState(() {
                      _currentCity = city;
                    });
                    Navigator.pop(context);
                  },
                );
              },
            ),
          ),
        );
      },
    );
  }

  Future<void> _loadCategories() async {
    try {
      final response = await _dioConsumer
          .get('https://backend.gatherpoint.sa/api/service_categories/list');
      if (response['data'] != null) {
        final List data = response['data'];
        setState(() {
          _categories =
              data.map((json) => ServiceCategory.fromJson(json)).toList();
          if (_categories.isNotEmpty) {
            _selectedCategoryId = _categories.first.id;
          }
        });
        if (_categories.isNotEmpty) {
          _fetchCategoryContent(_categories.first.id);
          _fetchReelsForNearbyItem(_categories.first.id);
        }
      }
    } catch (e) {
      print('خطأ في تحميل الأقسام: $e');
    }
  }

  Future<void> _fetchCategoryContent(int categoryId) async {
    try {
      final response = await _dioConsumer.get(
        'https://backend.gatherpoint.sa/api/service_categories/list?service_category_id=$categoryId',
      );

      if (response['data'] != null) {
        final List data = response['data'];
        setState(() {
          _categoryItems =
              data.map((json) => ServiceCategory.fromJson(json)).toList();
        });
      }
    } catch (e) {
      print('فشل تحميل بيانات القسم: $e');
    }
  }

  Future<void> _fetchReelsForNearbyItem(int itemId) async {
    try {
      final response = await _dioConsumer.get(
        'https://backend.gatherpoint.sa/api/items/list?service_category_id=$itemId&reels=1',
      );

      if (response['data'] != null) {
        final List data = response['data'];
        setState(() {
          _reelItems = data.cast<Map<String, dynamic>>();
        });
      }
    } catch (e) {
      print('فشل تحميل بيانات الريلز من العنصر المختار: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return Scaffold(
      backgroundColor: context.backgroundColor,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: ListView(
            children: [
              // Enhanced Logo & Settings Header
              Container(
                padding:
                    const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
                decoration: BoxDecoration(
                  color: context.cardColor,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: context.cardShadow,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: context.accentColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Image.asset(
                            AppAssets.imagesLogoCircle,
                            width: 40,
                            height: 40,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              s.appName,
                              style: AppTextStyles.font18Bold.copyWith(
                                color: context.primaryTextColor,
                              ),
                            ),
                            const SizedBox(height: 4),
                            GestureDetector(
                              onTap:
                                  _cities.isNotEmpty ? _showCityDialog : null,
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: context.accentColor
                                      .withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.location_on_outlined,
                                      size: 14,
                                      color: context.accentColor,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      _currentCity?.name ?? s.detectingLocation,
                                      style:
                                          AppTextStyles.font12Medium.copyWith(
                                        color: context.accentColor,
                                      ),
                                    ),
                                    const SizedBox(width: 4),
                                    Icon(
                                      Icons.keyboard_arrow_down,
                                      size: 16,
                                      color: context.accentColor,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    Container(
                      decoration: BoxDecoration(
                        color: context.accentColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: IconButton(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const SettingsView(),
                            ),
                          );
                        },
                        icon: Icon(
                          Icons.settings_outlined,
                          size: 24,
                          color: context.accentColor,
                        ),
                        tooltip: s.settings,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),
              // Enhanced Search Bar
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: context.accentColor.withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: TextField(
                  style: TextStyle(color: context.primaryTextColor),
                  decoration: InputDecoration(
                    hintText: s.searchHint,
                    hintStyle: AppTextStyles.font16Regular.copyWith(
                      color: context.secondaryTextColor,
                    ),
                    prefixIcon: Container(
                      padding: const EdgeInsets.all(12),
                      child: Icon(
                        Icons.search_outlined,
                        color: context.accentColor,
                        size: 24,
                      ),
                    ),
                    suffixIcon: Container(
                      margin: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: context.accentColor,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: IconButton(
                        onPressed: () {
                          // Add search functionality
                        },
                        icon: const Icon(
                          Icons.tune,
                          color: Colors.white,
                          size: 20,
                        ),
                        tooltip: s.filter,
                      ),
                    ),
                    filled: true,
                    fillColor: context.cardColor,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(20),
                      borderSide: BorderSide.none,
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(20),
                      borderSide: BorderSide.none,
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(20),
                      borderSide:
                          BorderSide(color: context.accentColor, width: 2),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      vertical: 16,
                      horizontal: 20,
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Filters Row
              SizedBox(
                height: 50,
                child: ListView.separated(
                  scrollDirection: Axis.horizontal,
                  itemCount: _categories.length,
                  separatorBuilder: (_, __) => const SizedBox(width: 10),
                  itemBuilder: (context, index) {
                    final category = _categories[index];
                    final isSelected = category.id == _selectedCategoryId;
                    return filterButton(
                      category.title,
                      category.icon,
                      context,
                      isSelected,
                      () {
                        setState(() {
                          _selectedCategoryId = category.id;
                        });
                        _fetchCategoryContent(category.id);
                        _fetchReelsForNearbyItem(category.id);
                      },
                    );
                  },
                ),
              ),

              const SizedBox(height: 32),

              // Enhanced Reels Section Header
              _buildSectionHeader(
                context,
                title: s.browseReels,
                icon: Icons.play_circle_outline,
                onViewAll: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ReelsPage(
                        searchResults: _reelItems,
                        searchQuery: '',
                        serviceCategoryId: 0,
                      ),
                    ),
                  );
                },
              ),

              const SizedBox(height: 20),
              SizedBox(
                height: 200,
                child: _reelItems.isEmpty
                    ? _buildEmptyState(
                        context,
                        icon: Icons.play_circle_outline,
                        message: s.noResults,
                      )
                    : ListView.builder(
                        scrollDirection: Axis.horizontal,
                        padding: const EdgeInsets.symmetric(horizontal: 4),
                        itemCount: _reelItems.length,
                        itemBuilder: (context, index) {
                          final item = _reelItems[index];
                          return _buildEnhancedReelCard(item);
                        },
                      ),
              ),

              const SizedBox(height: 32),

              // Enhanced Categories Section Header
              _buildSectionHeader(
                context,
                title: s.exploreCategories,
                icon: Icons.explore_outlined,
                onViewAll: () {
                  // Navigate to all categories
                },
              ),

              const SizedBox(height: 20),

              SizedBox(
                height: 140,
                child: _categoryItems.isEmpty
                    ? _buildEmptyState(
                        context,
                        icon: Icons.explore_outlined,
                        message: s.noResults,
                      )
                    : ListView.builder(
                        scrollDirection: Axis.horizontal,
                        padding: const EdgeInsets.symmetric(horizontal: 4),
                        itemCount: _categoryItems.length,
                        itemBuilder: (context, index) {
                          final item = _categoryItems[index];
                          final title = item.title;
                          return GestureDetector(
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => ExploreListScreen(
                                    dioConsumer: _dioConsumer,
                                    categoryId: item.id,
                                    categoryTitle: title,
                                  ),
                                ),
                              );
                            },
                            child: _buildEnhancedPlaceCard(
                                title, item.icon, context),
                          );
                        },
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget filterButton(String label, String imageUrl, BuildContext context,
      bool selected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 10),
        decoration: BoxDecoration(
          color: selected
              ? context.accentColor
              : (context.isDarkMode ? Colors.grey[800] : Colors.grey[200]),
          borderRadius: BorderRadius.circular(20),
          boxShadow: selected
              ? [
                  BoxShadow(
                    color: context.accentColor.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(6),
              child: Image.network(
                imageUrl,
                width: 24,
                height: 24,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Icon(
                    Icons.image_not_supported,
                    size: 24,
                    color: context.secondaryTextColor),
              ),
            ),
            const SizedBox(width: 6),
            Text(label,
                style: AppTextStyles.font12Bold.copyWith(
                    color: selected
                        ? (context.isDarkMode
                            ? AppColors.black
                            : AppColors.white)
                        : context.primaryTextColor)),
          ],
        ),
      ),
    );
  }

  Widget reelCard(Map<String, dynamic> item) {
    final image = (item['gallery'] as List).isNotEmpty
        ? item['gallery'][0]['image']
        : 'https://via.placeholder.com/260x180';
    final price = item['price'] ?? 0;

    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ReelsPage(
              searchResults: _reelItems,
              searchQuery: '',
              serviceCategoryId: 0,
            ),
          ),
        );
      },
      child: SizedBox(
        width: 260,
        child: Container(
          margin: const EdgeInsets.only(left: 20),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15),
            image: DecorationImage(
              image: NetworkImage(image),
              fit: BoxFit.cover,
            ),
          ),
          child: Align(
            alignment: Alignment.topLeft,
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Chip(
                label: Text('$price ر.س'),
                backgroundColor: Colors.black87,
                labelStyle:
                    AppTextStyles.font12SemiBold.copyWith(color: Colors.white),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget placeCard(
      String label, String iconUrl, Color color, BuildContext context) {
    return Container(
      width: 120,
      height: 100,
      margin: const EdgeInsets.only(left: 20),
      decoration: BoxDecoration(
        color: context.cardColor,
        border: Border.all(color: color, width: 2),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.network(
            iconUrl,
            width: 60,
            height: 60,
            errorBuilder: (context, error, stackTrace) =>
                Icon(Icons.error, color: context.secondaryTextColor),
          ),
          const SizedBox(height: 10),
          Text(
            label,
            style: AppTextStyles.font14Regular
                .copyWith(color: context.primaryTextColor),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  /// Enhanced section header with icon and view all button
  Widget _buildSectionHeader(
    BuildContext context, {
    required String title,
    required IconData icon,
    VoidCallback? onViewAll,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: context.accentColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: context.accentColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              title,
              style: AppTextStyles.font20Bold.copyWith(
                color: context.primaryTextColor,
              ),
            ),
          ),
          if (onViewAll != null)
            TextButton.icon(
              onPressed: onViewAll,
              icon: Icon(
                Icons.arrow_forward_ios,
                size: 14,
                color: context.accentColor,
              ),
              label: Text(
                S.of(context).viewAll,
                style: AppTextStyles.font14Medium.copyWith(
                  color: context.accentColor,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Enhanced reel card with better UI
  Widget _buildEnhancedReelCard(Map<String, dynamic> item) {
    final image = (item['gallery'] as List).isNotEmpty
        ? item['gallery'][0]['image']
        : 'https://via.placeholder.com/260x180';
    final price = item['price'] ?? 0;
    final title = item['title'] ?? '';

    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ReelsPage(
              searchResults: _reelItems,
              searchQuery: '',
              serviceCategoryId: 0,
            ),
          ),
        );
      },
      child: Container(
        width: 280,
        margin: const EdgeInsets.only(right: 16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          boxShadow: context.cardShadow,
        ),
        child: Stack(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: Image.network(
                image,
                height: 200,
                width: 280,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  height: 200,
                  width: 280,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        context.accentColor.withValues(alpha: 0.3),
                        context.accentColor.withValues(alpha: 0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    Icons.play_circle_outline,
                    size: 48,
                    color: context.accentColor,
                  ),
                ),
              ),
            ),
            // Gradient overlay
            Container(
              height: 200,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withValues(alpha: 0.7),
                  ],
                ),
              ),
            ),
            // Content overlay
            Positioned(
              bottom: 16,
              left: 16,
              right: 16,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (title.isNotEmpty)
                    Text(
                      title,
                      style: AppTextStyles.font16Bold.copyWith(
                        color: Colors.white,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: context.accentColor,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          '$price ${S.of(context).perNight}',
                          style: AppTextStyles.font12Bold.copyWith(
                            color: Colors.white,
                          ),
                        ),
                      ),
                      const Spacer(),
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: const Icon(
                          Icons.play_arrow,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Empty state widget
  Widget _buildEmptyState(
    BuildContext context, {
    required IconData icon,
    required String message,
  }) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: context.accentColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              icon,
              size: 48,
              color: context.accentColor,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: AppTextStyles.font16Regular.copyWith(
              color: context.secondaryTextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Enhanced place card with better UI
  Widget _buildEnhancedPlaceCard(
      String label, String iconUrl, BuildContext context) {
    return Container(
      width: 140,
      height: 120,
      margin: const EdgeInsets.only(right: 16),
      decoration: BoxDecoration(
        color: context.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: context.cardShadow,
        border: Border.all(
          color: context.accentColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: context.accentColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Image.network(
              iconUrl,
              width: 32,
              height: 32,
              errorBuilder: (context, error, stackTrace) => Icon(
                Icons.category_outlined,
                size: 32,
                color: context.accentColor,
              ),
            ),
          ),
          const SizedBox(height: 12),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Text(
              label,
              style: AppTextStyles.font14Medium.copyWith(
                color: context.primaryTextColor,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
