import 'package:flutter/material.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'place_details_screen.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';

class ExploreListScreen extends StatefulWidget {
  final int categoryId;
  final String categoryTitle;
  final DioConsumer dioConsumer;

  const ExploreListScreen(
      {super.key,
      required this.categoryId,
      required this.categoryTitle,
      required this.dioConsumer});

  @override
  State<ExploreListScreen> createState() => _ExploreListScreenState();
}

class _ExploreListScreenState extends State<ExploreListScreen>
    with TickerProviderStateMixin {
  bool isLoading = true;
  bool isLoadingMore = false;
  bool hasMore = true;
  int currentPage = 1;

  List<Map<String, dynamic>> places = [];
  List<Map<String, dynamic>> filteredPlaces = [];
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();

  // Enhanced Filter Variables
  bool isFilterExpanded = false;
  bool isAppBarCollapsed = false;
  String selectedSortBy = 'price_low';
  RangeValues priceRange = const RangeValues(50, 1000);
  double selectedRating = 0.0;
  int selectedGuests = 1;
  List<String> selectedAmenities = [];
  String selectedLocation = '';

  // Animation Controllers
  late AnimationController _filterAnimationController;
  late AnimationController _topBarAnimationController;
  late Animation<Offset> _topBarSlideAnimation;

  // Available filter options
  final List<String> availableAmenities = [
    'WiFi',
    'Pool',
    'Parking',
    'Kitchen',
    'Air Conditioning',
    'Gym',
    'Pet Friendly',
    'Beach Access'
  ];

  final List<String> sortOptions = [
    'price_low',
    'price_high',
    'rating_high',
    'rating_low',
    'newest',
    'popular'
  ];

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _filterAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _topBarAnimationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    // Initialize animations
    _topBarSlideAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _topBarAnimationController,
      curve: Curves.easeOutBack,
    ));

    // Start animations
    _topBarAnimationController.forward();

    fetchPlaces();
    _scrollController.addListener(_onScroll);
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    _filterAnimationController.dispose();
    _topBarAnimationController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    if (!mounted) return;
    _applyFilters();
  }

  void _onScroll() {
    // Check if widget is still mounted before calling setState
    if (!mounted) return;

    // Check if app bar should be collapsed (when scrolled past expanded height)
    final isCollapsed = _scrollController.hasClients &&
        _scrollController.offset > (200 - kToolbarHeight);

    if (isCollapsed != isAppBarCollapsed) {
      setState(() {
        isAppBarCollapsed = isCollapsed;
      });
    }

    // Load more items when reaching bottom
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100 &&
        !isLoadingMore &&
        hasMore) {
      currentPage++;
      fetchPlaces();
    }
  }

  Future<void> fetchPlaces() async {
    final url =
        'https://backend.gatherpoint.sa/api/items/list?service_category_id=${widget.categoryId}&page=$currentPage';

    try {
      if (currentPage == 1) {
        setState(() => isLoading = true);
      } else {
        setState(() => isLoadingMore = true);
      }

      final response = await widget.dioConsumer.get(url);
      if (response['data'] != null) {
        final List data = response['data'];

        final fetchedPlaces = data.map<Map<String, dynamic>>((item) {
          final List facilities = item['facilities'] ?? [];

          return {
            "id": item['id'],
            "title": item['title'],
            "price": item['price'].toString(),
            "image": item['image'],
            "reviews": item['rating'] ?? 0,
            "baths": item['baths'] ?? 2,
            "bedrooms": item['beds'] ?? 2,
            "no_guests": item['no_guests'] ?? 4,
            "wifi": facilities.any((f) => f['title'] == 'wifi'),
            "city": item['city'] ?? '',
            "country": item['country'] ?? '',
            "description": item['content'] ?? '',
            "policy": item['booking_rules'] ?? '',
            "features": facilities.map<Map<String, dynamic>>((f) {
              return {"icon": Icons.check, "label": f['title']};
            }).toList(),
          };
        }).toList();

        if (mounted) {
          setState(() {
            if (currentPage == 1) {
              places = fetchedPlaces;
              // Initialize filtered places with all places on first load
              filteredPlaces = List.from(fetchedPlaces);
            } else {
              places.addAll(fetchedPlaces);
              // Update filtered places if no active filters
              if (_searchController.text.isEmpty &&
                  selectedRating == 0 &&
                  selectedGuests == 1 &&
                  priceRange.start == 0 &&
                  priceRange.end == 2000) {
                filteredPlaces = List.from(places);
              }
            }

            hasMore = fetchedPlaces.length >= 10;
            isLoading = false;
            isLoadingMore = false;
          });
        }
      }
    } catch (e) {
      print('خطأ في جلب الأماكن: $e');
      if (mounted) {
        setState(() {
          isLoading = false;
          isLoadingMore = false;
        });
      }
    }
  }

  String getValidImageUrl(String? url) {
    if (url == null || url.isEmpty || url == 'null') {
      return 'https://placehold.co/400x300';
    }
    return url;
  }

  // Enhanced Filter Methods
  void _applyFilters() {
    if (!mounted) return;
    setState(() {
      // Check if any filters are actually active
      final hasSearchFilter = _searchController.text.isNotEmpty;
      final hasPriceFilter = priceRange.start > 0 || priceRange.end < 2000;
      final hasRatingFilter = selectedRating > 0;
      final hasGuestFilter = selectedGuests > 1;

      // If no filters are active, show all places
      if (!hasSearchFilter &&
          !hasPriceFilter &&
          !hasRatingFilter &&
          !hasGuestFilter) {
        filteredPlaces = List.from(places);
      } else {
        // Apply filters only when there are active filters
        filteredPlaces = places.where((place) {
          // Search filter
          if (hasSearchFilter) {
            final searchQuery = _searchController.text.toLowerCase();
            final title = (place['title'] ?? '').toString().toLowerCase();
            final description =
                (place['description'] ?? '').toString().toLowerCase();
            if (!title.contains(searchQuery) &&
                !description.contains(searchQuery)) {
              return false;
            }
          }

          // Price filter
          if (hasPriceFilter) {
            final price =
                double.tryParse(place['price']?.toString() ?? '0') ?? 0;
            if (price < priceRange.start || price > priceRange.end) {
              return false;
            }
          }

          // Rating filter
          if (hasRatingFilter) {
            final rating =
                double.tryParse(place['reviews']?.toString() ?? '0') ?? 0;
            if (rating < selectedRating) {
              return false;
            }
          }

          // Guests filter
          if (hasGuestFilter) {
            final maxGuests =
                int.tryParse(place['no_guests']?.toString() ?? '1') ?? 1;
            if (maxGuests < selectedGuests) {
              return false;
            }
          }

          return true;
        }).toList();
      }

      // Apply sorting
      _applySorting();
    });
  }

  void _applySorting() {
    switch (selectedSortBy) {
      case 'price_low':
        filteredPlaces.sort((a, b) {
          final priceA = double.tryParse(a['price']?.toString() ?? '0') ?? 0;
          final priceB = double.tryParse(b['price']?.toString() ?? '0') ?? 0;
          return priceA.compareTo(priceB);
        });
        break;
      case 'price_high':
        filteredPlaces.sort((a, b) {
          final priceA = double.tryParse(a['price']?.toString() ?? '0') ?? 0;
          final priceB = double.tryParse(b['price']?.toString() ?? '0') ?? 0;
          return priceB.compareTo(priceA);
        });
        break;
      case 'rating_high':
        filteredPlaces.sort((a, b) {
          final ratingA = double.tryParse(a['reviews']?.toString() ?? '0') ?? 0;
          final ratingB = double.tryParse(b['reviews']?.toString() ?? '0') ?? 0;
          return ratingB.compareTo(ratingA);
        });
        break;
    }
  }

  void _toggleFilter() {
    if (!mounted) return;
    setState(() {
      isFilterExpanded = !isFilterExpanded;
    });

    if (isFilterExpanded) {
      _filterAnimationController.forward();
    } else {
      _filterAnimationController.reverse();
    }
  }

  void _resetFilters() {
    if (!mounted) return;
    setState(() {
      _searchController.clear();
      selectedSortBy = 'price_low';
      priceRange = const RangeValues(50, 1000);
      selectedRating = 0.0;
      selectedGuests = 1;
      selectedAmenities.clear();
      selectedLocation = '';
    });
    _applyFilters();
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    // Always show filtered places (which will be same as places when no filters are active)
    final displayPlaces = filteredPlaces.isNotEmpty ? filteredPlaces : places;

    return Scaffold(
      backgroundColor: isDark ? Colors.black : const Color(0xFFF5F6FA),
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // Enhanced App Bar with Animation
          SliverAppBar(
            expandedHeight: 200,
            floating: false,
            pinned: true,
            elevation: 0,
            backgroundColor: theme.colorScheme.primary,
            // Title for collapsed state - only show when collapsed
            title: AnimatedOpacity(
              opacity: isAppBarCollapsed ? 1.0 : 0.0,
              duration: const Duration(milliseconds: 200),
              child: Text(
                widget.categoryTitle,
                style: AppTextStyles.font18Bold.copyWith(
                  color: theme.colorScheme.onPrimary,
                ),
              ),
            ),
            centerTitle: true,
            // Add shadow when collapsed
            shadowColor: Colors.black.withValues(alpha: 0.1),
            surfaceTintColor: Colors.transparent,
            flexibleSpace: FlexibleSpaceBar(
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      theme.colorScheme.primary,
                      theme.colorScheme.primary.withValues(alpha: 0.8),
                    ],
                  ),
                ),
                child: Stack(
                  children: [
                    // Background Pattern (Custom Geometric Pattern)
                    Positioned.fill(
                      child: Opacity(
                        opacity: 0.1,
                        child: CustomPaint(
                          painter: GeometricPatternPainter(
                            patternColor: theme.colorScheme.onPrimary,
                          ),
                        ),
                      ),
                    ),
                    // Content with proper padding
                    Padding(
                      padding: const EdgeInsets.fromLTRB(24, 80, 20, 20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SlideTransition(
                            position: _topBarSlideAnimation,
                            child: Text(
                              widget.categoryTitle,
                              style: AppTextStyles.font28Bold.copyWith(
                                color: Colors.white,
                              ),
                            ),
                          ),
                          const SizedBox(height: 8),
                          SlideTransition(
                            position: _topBarSlideAnimation,
                            child: Text(
                              '${displayPlaces.length} مكان متاح',
                              style: AppTextStyles.font16Medium.copyWith(
                                color: Colors.white.withValues(alpha: 0.9),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            leading: IconButton(
              icon: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: theme.colorScheme.onPrimary.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.arrow_back_ios,
                  color: theme.colorScheme.onPrimary,
                  size: 20,
                ),
              ),
              onPressed: () => Navigator.pop(context),
            ),
            actions: [
              // Logo in action bar - much bigger and properly centered
              Padding(
                padding: const EdgeInsets.only(right: 16),
                child: Center(
                  child: Container(
                    width: 60,
                    height: 60,
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.onPrimary.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Center(
                      child: Image.asset(
                        'assets/images/logo_circle.png',
                        width: 52,
                        height: 52,
                        fit: BoxFit.contain,
                        errorBuilder: (context, error, stackTrace) => Icon(
                          Icons.location_city,
                          color: theme.colorScheme.onPrimary,
                          size: 48,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),

          // Enhanced Search and Filter Bar
          SliverToBoxAdapter(
            child: Container(
              margin: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // Search Bar
                  Container(
                    decoration: BoxDecoration(
                      color: theme.cardColor,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        prefixIcon: Icon(
                          Icons.search,
                          color: theme.colorScheme.primary,
                          size: 24,
                        ),
                        hintText: s.searchHint,
                        hintStyle: AppTextStyles.font16Regular.copyWith(
                          color: Colors.grey[500],
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(16),
                          borderSide: BorderSide.none,
                        ),
                        filled: true,
                        fillColor: theme.cardColor,
                        contentPadding: const EdgeInsets.symmetric(
                          vertical: 16,
                          horizontal: 20,
                        ),
                      ),
                      style: AppTextStyles.font16Regular.copyWith(
                        color: theme.textTheme.bodyLarge?.color,
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Filter and Sort Row
                  Row(
                    children: [
                      // Filter Button
                      Expanded(
                        child: Container(
                          decoration: BoxDecoration(
                            color: isFilterExpanded
                                ? theme.colorScheme.primary
                                : theme.cardColor,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              borderRadius: BorderRadius.circular(12),
                              onTap: _toggleFilter,
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 12,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.tune,
                                      color: isFilterExpanded
                                          ? theme.colorScheme.onPrimary
                                          : theme.colorScheme.primary,
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      s.filter,
                                      style:
                                          AppTextStyles.font14SemiBold.copyWith(
                                        color: isFilterExpanded
                                            ? theme.colorScheme.onPrimary
                                            : theme.colorScheme.primary,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(width: 12),

                      // Sort Dropdown
                      Expanded(
                        child: Container(
                          decoration: BoxDecoration(
                            color: theme.cardColor,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: DropdownButtonHideUnderline(
                            child: DropdownButton<String>(
                              value: selectedSortBy,
                              isExpanded: true,
                              icon: Icon(
                                Icons.keyboard_arrow_down,
                                color: theme.colorScheme.primary,
                              ),
                              style: AppTextStyles.font14SemiBold.copyWith(
                                color: theme.textTheme.bodyLarge?.color,
                              ),
                              dropdownColor: theme.cardColor,
                              borderRadius: BorderRadius.circular(12),
                              items: const [
                                DropdownMenuItem(
                                  value: 'price_low',
                                  child: Padding(
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 16),
                                    child: Text(
                                      'السعر: الأقل أولاً',
                                      style: AppTextStyles.font14Regular,
                                    ),
                                  ),
                                ),
                                DropdownMenuItem(
                                  value: 'price_high',
                                  child: Padding(
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 16),
                                    child: Text(
                                      'السعر: الأعلى أولاً',
                                      style: AppTextStyles.font14Regular,
                                    ),
                                  ),
                                ),
                                DropdownMenuItem(
                                  value: 'rating_high',
                                  child: Padding(
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 16),
                                    child: Text(
                                      'التقييم: الأعلى أولاً',
                                      style: AppTextStyles.font14Regular,
                                    ),
                                  ),
                                ),
                              ],
                              onChanged: (value) {
                                if (value != null) {
                                  setState(() {
                                    selectedSortBy = value;
                                  });
                                  _applyFilters();
                                }
                              },
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // Expandable Filter Section
          SliverToBoxAdapter(
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              height: isFilterExpanded ? null : 0,
              child: isFilterExpanded
                  ? Container(
                      margin: const EdgeInsets.symmetric(horizontal: 16),
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: theme.cardColor,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 10,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Filter Header
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'تصفية النتائج',
                                style: AppTextStyles.font18Bold.copyWith(
                                  color: theme.textTheme.bodyLarge?.color,
                                ),
                              ),
                              TextButton(
                                onPressed: _resetFilters,
                                child: Text(
                                  'إعادة تعيين',
                                  style: AppTextStyles.font14SemiBold.copyWith(
                                    color: theme.colorScheme.primary,
                                  ),
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 20),

                          // Price Range Filter
                          Text(
                            'نطاق السعر',
                            style: AppTextStyles.font16SemiBold.copyWith(
                              color: theme.textTheme.bodyLarge?.color,
                            ),
                          ),
                          const SizedBox(height: 12),
                          RangeSlider(
                            values: priceRange,
                            min: 0,
                            max: 2000,
                            divisions: 40,
                            activeColor: theme.colorScheme.primary,
                            inactiveColor: theme.colorScheme.primary
                                .withValues(alpha: 0.3),
                            labels: RangeLabels(
                              '${priceRange.start.round()}',
                              '${priceRange.end.round()}',
                            ),
                            onChanged: (values) {
                              setState(() {
                                priceRange = values;
                              });
                              _applyFilters();
                            },
                          ),

                          const SizedBox(height: 20),

                          // Rating Filter
                          Text(
                            'التقييم الأدنى',
                            style: AppTextStyles.font16SemiBold.copyWith(
                              color: theme.textTheme.bodyLarge?.color,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: List.generate(5, (index) {
                              final rating = index + 1.0;
                              return GestureDetector(
                                onTap: () {
                                  setState(() {
                                    selectedRating =
                                        selectedRating == rating ? 0.0 : rating;
                                  });
                                  _applyFilters();
                                },
                                child: Container(
                                  margin: const EdgeInsets.only(right: 8),
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 8,
                                  ),
                                  decoration: BoxDecoration(
                                    color: selectedRating >= rating
                                        ? theme.colorScheme.primary
                                        : Colors.grey[200],
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        Icons.star,
                                        size: 16,
                                        color: selectedRating >= rating
                                            ? theme.colorScheme.onPrimary
                                            : Colors.grey[600],
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        '$rating',
                                        style: AppTextStyles.font12SemiBold
                                            .copyWith(
                                          color: selectedRating >= rating
                                              ? theme.colorScheme.onPrimary
                                              : Colors.grey[600],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            }),
                          ),

                          const SizedBox(height: 20),

                          // Guests Filter
                          Text(
                            'عدد الضيوف',
                            style: AppTextStyles.font16SemiBold.copyWith(
                              color: theme.textTheme.bodyLarge?.color,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              IconButton(
                                onPressed: selectedGuests > 1
                                    ? () {
                                        setState(() {
                                          selectedGuests--;
                                        });
                                        _applyFilters();
                                      }
                                    : null,
                                icon: Icon(
                                  Icons.remove_circle_outline,
                                  color: selectedGuests > 1
                                      ? theme.colorScheme.primary
                                      : Colors.grey[400],
                                ),
                              ),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 8,
                                ),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                      color: theme.colorScheme.primary),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Text(
                                  '$selectedGuests',
                                  style: AppTextStyles.font16SemiBold.copyWith(
                                    color: theme.colorScheme.primary,
                                  ),
                                ),
                              ),
                              IconButton(
                                onPressed: () {
                                  setState(() {
                                    selectedGuests++;
                                  });
                                  _applyFilters();
                                },
                                icon: Icon(
                                  Icons.add_circle_outline,
                                  color: theme.colorScheme.primary,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    )
                  : const SizedBox.shrink(),
            ),
          ),

          // Loading State
          if (isLoading)
            SliverToBoxAdapter(
              child: SizedBox(
                height: 400,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                          theme.colorScheme.primary),
                      strokeWidth: 3,
                    ),
                    const SizedBox(height: 20),
                    Text(
                      s.loading,
                      style: AppTextStyles.font16Medium.copyWith(
                        color: theme.textTheme.bodyLarge?.color,
                      ),
                    ),
                  ],
                ),
              ),
            ),

          // Empty State
          if (!isLoading && displayPlaces.isEmpty)
            SliverToBoxAdapter(
              child: SizedBox(
                height: 400,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.search_off_rounded,
                      size: 80,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 20),
                    Text(
                      s.noResults,
                      style: AppTextStyles.font18Bold.copyWith(
                        color: theme.textTheme.bodyLarge?.color,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'جرب تعديل معايير البحث',
                      style: AppTextStyles.font14Regular.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ),

          // Enhanced Grid View
          if (!isLoading && displayPlaces.isNotEmpty)
            SliverPadding(
              padding: const EdgeInsets.all(16),
              sliver: SliverGrid(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 20,
                  // Removed childAspectRatio to allow flexible card height
                ),
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    if (index >= displayPlaces.length) {
                      return const Center(
                        child: CircularProgressIndicator(),
                      );
                    }

                    final place = displayPlaces[index];
                    return TweenAnimationBuilder<double>(
                      duration: const Duration(milliseconds: 200),
                      tween: Tween(begin: 1.0, end: 1.0),
                      builder: (context, scale, child) {
                        return Transform.scale(
                          scale: scale,
                          child: GestureDetector(
                            onTapDown: (_) {
                              // Add subtle scale animation on tap
                            },
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) =>
                                      PlaceDetailsScreen(placeData: place),
                                ),
                              );
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(20),
                                color: theme.cardColor,
                                boxShadow: [
                                  BoxShadow(
                                    color: isDark
                                        ? Colors.black.withValues(alpha: 0.3)
                                        : Colors.black.withValues(alpha: 0.08),
                                    blurRadius: 20,
                                    offset: const Offset(0, 8),
                                    spreadRadius: 0,
                                  ),
                                  BoxShadow(
                                    color: isDark
                                        ? Colors.black.withValues(alpha: 0.2)
                                        : Colors.black.withValues(alpha: 0.04),
                                    blurRadius: 6,
                                    offset: const Offset(0, 2),
                                    spreadRadius: 0,
                                  ),
                                ],
                              ),
                              child: Stack(
                                children: [
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      // Image with fixed height
                                      Container(
                                        height: 120, // Fixed image height
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              const BorderRadius.vertical(
                                                  top: Radius.circular(20)),
                                          boxShadow: [
                                            BoxShadow(
                                              color: Colors.black
                                                  .withValues(alpha: 0.1),
                                              blurRadius: 8,
                                              offset: const Offset(0, 2),
                                            ),
                                          ],
                                        ),
                                        child: Stack(
                                          children: [
                                            ClipRRect(
                                              borderRadius:
                                                  const BorderRadius.vertical(
                                                      top: Radius.circular(20)),
                                              child: Image.network(
                                                getValidImageUrl(
                                                    place["image"]),
                                                height: 120,
                                                width: double.infinity,
                                                fit: BoxFit.cover,
                                                errorBuilder: (context, error,
                                                        stackTrace) =>
                                                    Container(
                                                  height: 120,
                                                  width: double.infinity,
                                                  decoration: BoxDecoration(
                                                    gradient: LinearGradient(
                                                      begin: Alignment.topLeft,
                                                      end:
                                                          Alignment.bottomRight,
                                                      colors: [
                                                        Colors.grey[300]!,
                                                        Colors.grey[100]!,
                                                      ],
                                                    ),
                                                  ),
                                                  child: const Icon(
                                                    Icons
                                                        .image_not_supported_outlined,
                                                    size: 48,
                                                    color: Colors.grey,
                                                  ),
                                                ),
                                                loadingBuilder: (context, child,
                                                    loadingProgress) {
                                                  if (loadingProgress == null) {
                                                    return child;
                                                  }
                                                  return Container(
                                                    height: 120,
                                                    width: double.infinity,
                                                    decoration: BoxDecoration(
                                                      color: Colors.grey[100],
                                                      borderRadius:
                                                          const BorderRadius
                                                              .vertical(
                                                              top: Radius
                                                                  .circular(
                                                                      20)),
                                                    ),
                                                    child: const Center(
                                                      child:
                                                          CircularProgressIndicator(
                                                        strokeWidth: 2,
                                                        valueColor:
                                                            AlwaysStoppedAnimation<
                                                                    Color>(
                                                                Colors.grey),
                                                      ),
                                                    ),
                                                  );
                                                },
                                              ),
                                            ),
                                            // Gradient overlay for better text readability
                                            Container(
                                              height: 120,
                                              decoration: BoxDecoration(
                                                borderRadius: const BorderRadius
                                                    .vertical(
                                                    top: Radius.circular(20)),
                                                gradient: LinearGradient(
                                                  begin: Alignment.topCenter,
                                                  end: Alignment.bottomCenter,
                                                  colors: [
                                                    Colors.transparent,
                                                    Colors.black
                                                        .withValues(alpha: 0.1),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      // Content area (flexible height)
                                      Padding(
                                        padding: const EdgeInsets.all(16),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            // Location with icon
                                            Row(
                                              children: [
                                                Icon(
                                                  Icons.location_on_outlined,
                                                  size: 16,
                                                  color: Colors.grey[600],
                                                ),
                                                const SizedBox(width: 4),
                                                Expanded(
                                                  child: Text(
                                                    "${place["city"]} - ${place["country"]}",
                                                    style: AppTextStyles
                                                        .font14Bold
                                                        .copyWith(
                                                      color: theme.textTheme
                                                          .bodyLarge?.color,
                                                      height: 1.2,
                                                    ),
                                                    maxLines: 1,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                  ),
                                                ),
                                              ],
                                            ),
                                            const SizedBox(height: 8),
                                            // Price
                                            Container(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 8,
                                                      vertical: 4),
                                              decoration: BoxDecoration(
                                                color: Colors.amber
                                                    .withValues(alpha: 0.1),
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                              ),
                                              child: Text(
                                                '${place["price"]} ${s.perNight}',
                                                style: AppTextStyles.font14Bold
                                                    .copyWith(
                                                  color: Colors.amber[800],
                                                  height: 1.0,
                                                ),
                                              ),
                                            ),
                                            const SizedBox(height: 8),
                                            // Bottom row with rating and amenities
                                            Row(
                                              children: [
                                                // Rating
                                                Container(
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      horizontal: 8,
                                                      vertical: 4),
                                                  decoration: BoxDecoration(
                                                    color: Colors.amber
                                                        .withValues(alpha: 0.1),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            12),
                                                  ),
                                                  child: Row(
                                                    mainAxisSize:
                                                        MainAxisSize.min,
                                                    children: [
                                                      const Icon(Icons.star,
                                                          size: 14,
                                                          color: Colors.amber),
                                                      const SizedBox(width: 4),
                                                      Text(
                                                        '${place["reviews"]}',
                                                        style: AppTextStyles
                                                            .font12Bold
                                                            .copyWith(
                                                          color:
                                                              Colors.amber[800],
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                const Spacer(),
                                                // Amenities
                                                Row(
                                                  children: [
                                                    if (place["wifi"])
                                                      Container(
                                                        padding:
                                                            const EdgeInsets
                                                                .all(6),
                                                        decoration:
                                                            BoxDecoration(
                                                          color: Colors.blue
                                                              .withValues(
                                                                  alpha: 0.1),
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(8),
                                                        ),
                                                        child: const Icon(
                                                          Icons.wifi,
                                                          size: 14,
                                                          color: Colors.blue,
                                                        ),
                                                      ),
                                                    const SizedBox(width: 4),
                                                    Container(
                                                      padding:
                                                          const EdgeInsets.all(
                                                              6),
                                                      decoration: BoxDecoration(
                                                        color: Colors.green
                                                            .withValues(
                                                                alpha: 0.1),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(8),
                                                      ),
                                                      child: Row(
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        children: [
                                                          const Icon(
                                                            Icons
                                                                .people_outline,
                                                            size: 12,
                                                            color: Colors.green,
                                                          ),
                                                          const SizedBox(
                                                              width: 2),
                                                          Text(
                                                            '${place["no_guests"]}',
                                                            style: AppTextStyles
                                                                .font10Bold
                                                                .copyWith(
                                                              color: Colors
                                                                  .green[700],
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  // Enhanced Favorite Button
                                  Positioned(
                                    top: 12,
                                    right: 12,
                                    child: Container(
                                      decoration: BoxDecoration(
                                        color: theme.cardColor,
                                        shape: BoxShape.circle,
                                        boxShadow: [
                                          BoxShadow(
                                            color: isDark
                                                ? Colors.black
                                                    .withValues(alpha: 0.4)
                                                : Colors.black
                                                    .withValues(alpha: 0.15),
                                            blurRadius: 8,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: Material(
                                        color: Colors.transparent,
                                        shape: const CircleBorder(),
                                        child: InkWell(
                                          borderRadius:
                                              BorderRadius.circular(20),
                                          onTap: () {
                                            // Add favorite functionality
                                          },
                                          child: Padding(
                                            padding: const EdgeInsets.all(8),
                                            child: Icon(
                                              Icons.favorite_border,
                                              color: Colors.red[400],
                                              size: 20,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    );
                  },
                  childCount: displayPlaces.length + (isLoadingMore ? 1 : 0),
                ),
              ),
            ),

          // Loading More Indicator
          if (isLoadingMore)
            SliverToBoxAdapter(
              child: Container(
                padding: const EdgeInsets.all(20),
                child: Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                        theme.colorScheme.primary),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Custom painter for geometric pattern background
class GeometricPatternPainter extends CustomPainter {
  final Color patternColor;

  GeometricPatternPainter({this.patternColor = Colors.white});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = patternColor.withValues(alpha: 0.3)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    const double spacing = 40.0;

    // Draw diagonal lines pattern
    for (double i = -size.height; i < size.width + size.height; i += spacing) {
      canvas.drawLine(
        Offset(i, 0),
        Offset(i + size.height, size.height),
        paint,
      );
    }

    // Draw reverse diagonal lines for diamond pattern
    for (double i = 0; i < size.width + size.height; i += spacing) {
      canvas.drawLine(
        Offset(i, 0),
        Offset(i - size.height, size.height),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
