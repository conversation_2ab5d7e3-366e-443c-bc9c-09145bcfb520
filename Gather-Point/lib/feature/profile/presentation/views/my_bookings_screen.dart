import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/core/widgets/enhanced_layouts.dart';
import 'package:gather_point/generated/l10n.dart';

class MyBookingsScreen extends StatefulWidget {
  const MyBookingsScreen({super.key});

  @override
  State<MyBookingsScreen> createState() => _MyBookingsScreenState();
}

class _MyBookingsScreenState extends State<MyBookingsScreen> {
  final List<Map<String, dynamic>> _mockBookings = [
    {
      'id': '1',
      'propertyName': 'شقة فاخرة في الرياض',
      'location': 'الرياض، حي النخيل',
      'checkIn': '2024-01-15',
      'checkOut': '2024-01-20',
      'guests': 4,
      'totalPrice': 1500.0,
      'status': 'confirmed',
      'image': 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=400',
    },
    {
      'id': '2',
      'propertyName': 'فيلا مع مسبح',
      'location': 'جدة، حي الشاطئ',
      'checkIn': '2024-02-10',
      'checkOut': '2024-02-15',
      'guests': 6,
      'totalPrice': 2800.0,
      'status': 'pending',
      'image': 'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=400',
    },
    {
      'id': '3',
      'propertyName': 'استوديو عصري',
      'location': 'الدمام، الكورنيش',
      'checkIn': '2024-01-05',
      'checkOut': '2024-01-08',
      'guests': 2,
      'totalPrice': 800.0,
      'status': 'completed',
      'image': 'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=400',
    },
  ];

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return EnhancedPageLayout(
      title: s.myBookings,
      showBackButton: false,
      hasBottomNavigation: true,
      body: _mockBookings.isEmpty
          ? EnhancedEmptyState(
              icon: Icons.calendar_today_outlined,
              title: 'لا توجد حجوزات',
              subtitle: 'لم تقم بأي حجوزات حتى الآن',
              actionText: 'استكشف العقارات',
              onActionPressed: () {
                // Navigate to search/home
              },
            )
          : EnhancedScrollablePageLayout(
              hasBottomNavigation: true,
              padding: const EdgeInsets.all(16),
              children: [
                // Stats Cards
                Row(
                  children: [
                    Expanded(
                      child: _StatCard(
                        title: s.totalBookings,
                        value: '${_mockBookings.length}',
                        icon: Icons.calendar_today_rounded,
                        color: context.accentColor,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _StatCard(
                        title: s.confirmedBookings,
                        value: '${_mockBookings.where((b) => b['status'] == 'confirmed').length}',
                        icon: Icons.check_circle_rounded,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // Filter Tabs
                _FilterTabs(),
                const SizedBox(height: 16),

                // Bookings List
                ..._mockBookings.map((booking) => _BookingCard(booking: booking)),
              ],
            ),
    );
  }
}

class _StatCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;

  const _StatCard({
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: AppTextStyles.font24Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: AppTextStyles.font12Regular.copyWith(
              color: context.secondaryTextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class _FilterTabs extends StatefulWidget {
  @override
  State<_FilterTabs> createState() => _FilterTabsState();
}

class _FilterTabsState extends State<_FilterTabs> {
  int _selectedIndex = 0;
  late final List<String> _filters;

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    _filters = [s.all, s.confirmed, s.pending, s.completed];

    return SizedBox(
      height: 40,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: _filters.length,
        separatorBuilder: (_, __) => const SizedBox(width: 8),
        itemBuilder: (context, index) {
          final isSelected = index == _selectedIndex;
          return GestureDetector(
            onTap: () => setState(() => _selectedIndex = index),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
              decoration: BoxDecoration(
                color: isSelected ? context.accentColor : Colors.transparent,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: isSelected ? context.accentColor : context.secondaryTextColor.withValues(alpha: 0.3),
                ),
              ),
              child: Text(
                _filters[index],
                style: AppTextStyles.font14SemiBold.copyWith(
                  color: isSelected ? Colors.white : context.primaryTextColor,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

class _BookingCard extends StatelessWidget {
  final Map<String, dynamic> booking;

  const _BookingCard({required this.booking});

  Color _getStatusColor(String status) {
    switch (status) {
      case 'confirmed':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'completed':
        return Colors.blue;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String status, S s) {
    switch (status) {
      case 'confirmed':
        return s.confirmed;
      case 'pending':
        return s.pending;
      case 'completed':
        return s.completed;
      case 'cancelled':
        return s.cancelled;
      default:
        return 'غير معروف';
    }
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    final statusColor = _getStatusColor(booking['status']);

    return EnhancedCard(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Property Image and Status
          Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.network(
                  booking['image'],
                  height: 150,
                  width: double.infinity,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) => Container(
                    height: 150,
                    color: context.secondaryTextColor.withValues(alpha: 0.1),
                    child: Icon(
                      Icons.image_not_supported_rounded,
                      color: context.secondaryTextColor,
                      size: 48,
                    ),
                  ),
                ),
              ),
              Positioned(
                top: 12,
                right: 12,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: statusColor,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    _getStatusText(booking['status'], s),
                    style: AppTextStyles.font12SemiBold.copyWith(
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Property Details
          Text(
            booking['propertyName'],
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.location_on_rounded,
                size: 16,
                color: context.secondaryTextColor,
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  booking['location'],
                  style: AppTextStyles.font14Regular.copyWith(
                    color: context.secondaryTextColor,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Booking Details
          Row(
            children: [
              Expanded(
                child: _DetailItem(
                  icon: Icons.calendar_today_rounded,
                  label: s.checkIn,
                  value: booking['checkIn'],
                ),
              ),
              Expanded(
                child: _DetailItem(
                  icon: Icons.calendar_today_rounded,
                  label: s.checkOut,
                  value: booking['checkOut'],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _DetailItem(
                  icon: Icons.people_rounded,
                  label: s.numberOfGuests,
                  value: '${booking['guests']} ${s.guests}',
                ),
              ),
              Expanded(
                child: _DetailItem(
                  icon: Icons.attach_money_rounded,
                  label: s.totalPrice,
                  value: '${booking['totalPrice']} ر.س',
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Action Buttons
          Row(
            children: [
              Expanded(
                child: EnhancedButton(
                  text: s.viewDetails,
                  onPressed: () {},
                  isOutlined: true,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: EnhancedButton(
                  text: booking['status'] == 'pending' ? s.cancelBooking : s.rebookProperty,
                  onPressed: () {},
                  backgroundColor: booking['status'] == 'pending' ? Colors.red : context.accentColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _DetailItem extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;

  const _DetailItem({
    required this.icon,
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: context.accentColor,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppTextStyles.font10Regular.copyWith(
                  color: context.secondaryTextColor,
                ),
              ),
              Text(
                value,
                style: AppTextStyles.font12SemiBold.copyWith(
                  color: context.primaryTextColor,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
