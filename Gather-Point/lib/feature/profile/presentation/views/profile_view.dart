import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/routing/routes_branches.dart';
import 'package:gather_point/core/routing/routes_keys.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/theme/theme_cubit.dart';
import 'package:gather_point/core/utils/app_constants.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/core/widgets/enhanced_layouts.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:go_router/go_router.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'edit_profile_screen.dart';

class ProfileTabView extends StatefulWidget {
  const ProfileTabView({super.key});

  @override
  _ProfileTabViewState createState() => _ProfileTabViewState();
}

class _ProfileTabViewState extends State<ProfileTabView> {
  late UserEntity _user;

  @override
  void initState() {
    super.initState();
    _loadProfileData();
  }

  void _loadProfileData() {
    final box = Hive.box<UserEntity>(AppConstants.kMyProfileBoxName);
    _user = box.get(AppConstants.kMyProfileKey)!;
  }

  String _getGenderString(int genderCode) {
    switch (genderCode) {
      case 1:
        return 'ذكر';
      case 2:
        return 'أنثي';
      default:
        return 'غير محدد';
    }
  }

  void _navigateToEditProfile() async {
    final updatedUser = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditProfileScreen(user: _user),
      ),
    );

    if (updatedUser != null && updatedUser is UserEntity) {
      setState(() => _user = updatedUser);
    }
  }

  void _handleLogout() async {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('تسجيل الخروج'),
          content: const Text('هل أنت متأكد أنك تريد تسجيل الخروج؟'),
          actions: [
            TextButton(
              child: const Text('إلغاء'),
              onPressed: () => Navigator.of(context).pop(),
            ),
            TextButton(
              child: const Text('تسجيل خروج'),
              onPressed: () {
                Hive.box<UserEntity>(AppConstants.kMyProfileBoxName).clear();
                GoRouter.of(context).go(RoutesKeys.kLoginView);
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return EnhancedPageLayout(
      title: s.profile,
      showBackButton: false,
      hasBottomNavigation: true,
      actions: [
        Container(
          margin: const EdgeInsets.only(right: 8),
          decoration: BoxDecoration(
            color: context.accentColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: IconButton(
            icon: Icon(
              Icons.edit_rounded,
              color: context.accentColor,
            ),
            onPressed: _navigateToEditProfile,
            tooltip: 'تعديل الملف الشخصي',
          ),
        ),
      ],
      body: Column(
        children: [
          Expanded(
            child: _ProfileBody(user: _user, getGenderString: _getGenderString),
          ),
          // Enhanced Host Mode Switch
          EnhancedCard(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: context.accentColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.business_center_rounded,
                    color: context.accentColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'وضع المستضيف',
                        style: AppTextStyles.font16SemiBold.copyWith(
                          color: context.primaryTextColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'قم بتفعيل وضع المستضيف لإدارة عقاراتك',
                        style: AppTextStyles.font12Regular.copyWith(
                          color: context.secondaryTextColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Switch(
                  value: _user.isHosterMode,
                  onChanged: (value) {
                    setState(() {
                      _user = _user.copyWith(isHosterMode: value);
                      Hive.box<UserEntity>(AppConstants.kMyProfileBoxName)
                          .put(AppConstants.kMyProfileKey, _user);
                      isHosterModeNotifier.value = value;
                    });
                    // Reload the app after changing hoster mode
                    GoRouter.of(context).go(RoutesKeys.kSplashView);
                  },
                  activeColor: context.accentColor,
                ),
              ],
            ),
          ),
          // Enhanced Logout Button
          Container(
            padding: const EdgeInsets.all(16),
            child: EnhancedButton(
              text: 'تسجيل الخروج',
              icon: Icons.logout_rounded,
              onPressed: _handleLogout,
              backgroundColor: Colors.red,
              width: double.infinity,
            ),
          ),
        ],
      ),
    );
  }
}

class _ProfileBody extends StatelessWidget {
  final UserEntity user;
  final String Function(int) getGenderString;

  const _ProfileBody({
    required this.user,
    required this.getGenderString,
  });

  @override
  Widget build(BuildContext context) {
    if (user.isGuest) {
      return EnhancedEmptyState(
        icon: Icons.person_off_rounded,
        title: 'يجب عليك تسجيل الدخول',
        subtitle: 'قم بتسجيل الدخول لعرض حسابك الشخصي',
        actionText: 'تسجيل الدخول',
        onActionPressed: () {
          GoRouter.of(context).go(RoutesKeys.kLoginView);
        },
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Enhanced Profile Header
          EnhancedCard(
            child: Column(
              children: [
                _ProfileImage(image: user.image),
                const SizedBox(height: 20),
                Text(
                  user.fullName,
                  style: AppTextStyles.font24Bold.copyWith(
                    color: context.primaryTextColor,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  user.bio,
                  style: AppTextStyles.font16Regular.copyWith(
                    color: context.secondaryTextColor,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                // Stats Row
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _StatItem(
                      icon: Icons.star_rounded,
                      label: 'التقييم',
                      value: '4.8',
                      color: Colors.amber,
                    ),
                    Container(
                      width: 1,
                      height: 40,
                      color: context.secondaryTextColor.withValues(alpha: 0.2),
                    ),
                    _StatItem(
                      icon: Icons.home_rounded,
                      label: 'العقارات',
                      value: '12',
                      color: context.accentColor,
                    ),
                    Container(
                      width: 1,
                      height: 40,
                      color: context.secondaryTextColor.withValues(alpha: 0.2),
                    ),
                    _StatItem(
                      icon: Icons.calendar_today_rounded,
                      label: 'الحجوزات',
                      value: '45',
                      color: Colors.green,
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          // Enhanced Profile Info
          _ProfileInfo(user: user, getGenderString: getGenderString),
        ],
      ),
    );
  }
}

class _ProfileImage extends StatelessWidget {
  final String? image;

  const _ProfileImage({this.image});

  @override
  Widget build(BuildContext context) {
    return CircleAvatar(
      radius: 60,
      backgroundImage: _getImageProvider(),
      child: image == null
          ? Icon(Icons.person, size: 60, color: Colors.grey.shade400)
          : null,
    );
  }

  ImageProvider? _getImageProvider() {
    if (image == null) return null;
    if (image!.startsWith('http')) {
      return CachedNetworkImageProvider(image!);
    }
    return AssetImage(image!);
  }
}

class _ProfileInfo extends StatelessWidget {
  final UserEntity user;
  final String Function(int) getGenderString;

  const _ProfileInfo({
    required this.user,
    required this.getGenderString,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'المعلومات الشخصية',
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          _InfoTile(
            icon: Icons.email_rounded,
            title: 'البريد الإلكتروني',
            value: user.email,
            color: Colors.blue,
          ),
          _InfoTile(
            icon: Icons.phone_rounded,
            title: 'رقم الجوال',
            value: user.phone,
            color: Colors.green,
          ),
          _InfoTile(
            icon: Icons.person_outline_rounded,
            title: 'الجنس',
            value: getGenderString(user.gender),
            color: Colors.purple,
          ),
          _InfoTile(
            icon: Icons.cake_rounded,
            title: 'تاريخ الميلاد',
            value: user.birthdate,
            color: Colors.orange,
          ),
        ],
      ),
    );
  }
}

class _InfoTile extends StatelessWidget {
  final IconData icon;
  final String title;
  final String? value;
  final Color color;

  const _InfoTile({
    required this.icon,
    required this.title,
    required this.value,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.font12Regular.copyWith(
                    color: context.secondaryTextColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value ?? 'غير محدد',
                  style: AppTextStyles.font14SemiBold.copyWith(
                    color: context.primaryTextColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _StatItem extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;
  final Color color;

  const _StatItem({
    required this.icon,
    required this.label,
    required this.value,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: AppTextStyles.font18Bold.copyWith(
            color: context.primaryTextColor,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: AppTextStyles.font12Regular.copyWith(
            color: context.secondaryTextColor,
          ),
        ),
      ],
    );
  }
}