import 'package:flutter/material.dart';
import 'package:gather_point/feature/host/presentation/views/host_home_page.dart';

/// Simple test app to verify the Host Home Page works correctly
class HostHomeTestApp extends StatelessWidget {
  const HostHomeTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Host Home Test',
      theme: ThemeData(
        primarySwatch: Colors.amber,
        brightness: Brightness.light,
      ),
      darkTheme: ThemeData(
        primarySwatch: Colors.amber,
        brightness: Brightness.dark,
      ),
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Host Dashboard Test'),
          backgroundColor: Colors.amber,
        ),
        body: const HostHomePage(),
      ),
    );
  }
}

/// Test function to run the host home page
void main() {
  runApp(const HostHomeTestApp());
}

/// Example of how to integrate into your existing app:
/// 
/// ```dart
/// // In your main app navigation
/// Navigator.push(
///   context,
///   MaterialPageRoute(
///     builder: (context) => const HostHomePage(),
///   ),
/// );
/// 
/// // Or as a tab in bottom navigation
/// IndexedStack(
///   index: currentIndex,
///   children: [
///     HomePage(),
///     SearchPage(),
///     HostHomePage(), // Add as a tab
///     ProfilePage(),
///   ],
/// )
/// ```
