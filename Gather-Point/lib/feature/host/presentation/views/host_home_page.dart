import 'package:flutter/material.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';

class HostHomePage extends StatefulWidget {
  const HostHomePage({super.key});

  @override
  State<HostHomePage> createState() => _HostHomePageState();
}

class _HostHomePageState extends State<HostHomePage> {
  // Dummy data - to be replaced with API calls later
  final double walletBalance = 2450.75;
  final double totalEarnings = 15680.50;
  final double pendingEarnings = 890.25;
  final double totalWithdrawn = 12340.00;

  final List<Map<String, dynamic>> recentBookings = [
    {
      'id': 'BK001',
      'guestName': 'أحمد محمد',
      'propertyName': 'شقة فاخرة في الرياض',
      'checkIn': '2024-01-15',
      'checkOut': '2024-01-18',
      'nights': 3,
      'amount': 450.0,
      'status': 'confirmed',
    },
    {
      'id': 'BK002',
      'guestName': 'سارة أحمد',
      'propertyName': 'فيلا مع مسبح',
      'checkIn': '2024-01-20',
      'checkOut': '2024-01-25',
      'nights': 5,
      'amount': 750.0,
      'status': 'processing',
    },
    {
      'id': 'BK003',
      'guestName': 'محمد علي',
      'propertyName': 'استوديو حديث',
      'checkIn': '2024-01-12',
      'checkOut': '2024-01-14',
      'nights': 2,
      'amount': 280.0,
      'status': 'completed',
    },
  ];

  final List<Map<String, dynamic>> recentReviews = [
    {
      'id': 'RV001',
      'guestName': 'فاطمة الزهراء',
      'propertyName': 'شقة فاخرة في الرياض',
      'rating': 5.0,
      'comment': 'مكان رائع ونظيف، الخدمة ممتازة والموقع مثالي',
      'date': '2024-01-10',
    },
    {
      'id': 'RV002',
      'guestName': 'عبدالله السعد',
      'propertyName': 'فيلا مع مسبح',
      'rating': 4.5,
      'comment': 'إقامة جميلة، المسبح رائع والفيلا واسعة',
      'date': '2024-01-08',
    },
    {
      'id': 'RV003',
      'guestName': 'نورا أحمد',
      'propertyName': 'استوديو حديث',
      'rating': 4.8,
      'comment': 'مكان هادئ ومريح، مناسب للإقامة القصيرة',
      'date': '2024-01-05',
    },
  ];

  // Chart data (dummy data for now)
  final List<double> earningsData = [1200, 1800, 1500, 2200, 1900, 2450];
  final List<double> bookingsData = [8, 12, 10, 15, 13, 18];

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    
    return EnhancedPageLayout(
      hasBottomNavigation: true,
      title: 'لوحة المضيف',
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Wallet & Earnings Overview
            _buildFinancialOverview(context, s),
            
            const SizedBox(height: 24),
            
            // Charts Section
            _buildChartsSection(context, s),
            
            const SizedBox(height: 24),
            
            // Recent Bookings
            _buildRecentBookings(context, s),
            
            const SizedBox(height: 24),
            
            // Recent Reviews
            _buildRecentReviews(context, s),
            
            const SizedBox(height: 100), // Bottom padding for navigation
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialOverview(BuildContext context, S s) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'رصيد المحفظة',
          style: AppTextStyles.font20Bold.copyWith(
            color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
          ),
        ),
        const SizedBox(height: 16),
        
        // Main Balance Card
        EnhancedCard(
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.amber,
                  Colors.amber.withValues(alpha: 0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'الرصيد المتاح',
                          style: AppTextStyles.font14Regular.copyWith(
                            color: Colors.black.withValues(alpha: 0.8),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '\$${walletBalance.toStringAsFixed(2)}',
                          style: AppTextStyles.font28Bold.copyWith(
                            color: Colors.black,
                          ),
                        ),
                      ],
                    ),
                    EnhancedButton(
                      text: 'سحب',
                      onPressed: () => _showWithdrawDialog(context, s),
                      backgroundColor: Colors.black,
                      textColor: Colors.white,
                      icon: Icons.account_balance_wallet_rounded,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Stats Grid
        Row(
          children: [
            Expanded(
              child: _StatCard(
                title: 'إجمالي الأرباح',
                value: '\$${totalEarnings.toStringAsFixed(2)}',
                icon: Icons.trending_up_rounded,
                color: Colors.green,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _StatCard(
                title: 'الأرباح المعلقة',
                value: '\$${pendingEarnings.toStringAsFixed(2)}',
                icon: Icons.schedule_rounded,
                color: Colors.orange,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        Row(
          children: [
            Expanded(
              child: _StatCard(
                title: 'إجمالي المسحوب',
                value: '\$${totalWithdrawn.toStringAsFixed(2)}',
                icon: Icons.download_rounded,
                color: Colors.blue,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _StatCard(
                title: 'هذا الشهر',
                value: '\$${(walletBalance + 1200).toStringAsFixed(2)}',
                icon: Icons.calendar_month_rounded,
                color: Colors.amber,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildChartsSection(BuildContext context, S s) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نظرة عامة على الأرباح',
          style: AppTextStyles.font20Bold.copyWith(
            color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
          ),
        ),
        const SizedBox(height: 16),

        // Charts
        Row(
          children: [
            Expanded(
              child: _buildEarningsChart(context, s),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildBookingsChart(context, s),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildEarningsChart(BuildContext context, S s) {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'مخطط الأرباح',
              style: AppTextStyles.font16SemiBold.copyWith(
                color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              height: 120,
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.amber.withValues(alpha: 0.1),
                    Colors.amber.withValues(alpha: 0.05),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Stack(
                children: [
                  // Simple chart visualization
                  Positioned.fill(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: earningsData.isNotEmpty ? earningsData.map((value) {
                          final maxValue = earningsData.reduce((a, b) => a > b ? a : b);
                          return Container(
                            width: 8,
                            height: maxValue > 0 ? (value / maxValue) * 80 : 20,
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.secondary,
                              borderRadius: BorderRadius.circular(4),
                            ),
                          );
                        }).toList() : [
                          Container(
                            width: 8,
                            height: 20,
                            decoration: BoxDecoration(
                              color: Colors.grey,
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  // Chart placeholder text
                  Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.trending_up_rounded,
                          color: Theme.of(context).colorScheme.secondary,
                          size: 32,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'الأرباح الشهرية',
                          style: AppTextStyles.font12Regular.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBookingsChart(BuildContext context, S s) {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'مخطط الحجوزات',
              style: AppTextStyles.font16SemiBold.copyWith(
                color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              height: 120,
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.blue.withValues(alpha: 0.1),
                    Colors.blue.withValues(alpha: 0.05),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Stack(
                children: [
                  // Simple chart visualization
                  Positioned.fill(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: bookingsData.isNotEmpty ? bookingsData.map((value) {
                          final maxValue = bookingsData.reduce((a, b) => a > b ? a : b);
                          return Container(
                            width: 8,
                            height: maxValue > 0 ? (value / maxValue) * 80 : 20,
                            decoration: BoxDecoration(
                              color: Colors.blue,
                              borderRadius: BorderRadius.circular(4),
                            ),
                          );
                        }).toList() : [
                          Container(
                            width: 8,
                            height: 20,
                            decoration: BoxDecoration(
                              color: Colors.grey,
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  // Chart placeholder text
                  const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.calendar_today_rounded,
                          color: Colors.blue,
                          size: 32,
                        ),
                        SizedBox(height: 8),
                        Text(
                          'الحجوزات الشهرية',
                          style: TextStyle(
                            color: Colors.grey,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentBookings(BuildContext context, S s) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'أحدث الحجوزات',
              style: AppTextStyles.font20Bold.copyWith(
                color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
              ),
            ),
            TextButton(
              onPressed: () {
                // Navigate to all bookings
              },
              child: Text(
                'عرض جميع الحجوزات',
                style: AppTextStyles.font14Regular.copyWith(
                  color: Colors.amber,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        if (recentBookings.isEmpty)
          EnhancedCard(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Center(
                child: Text(
                  'لا توجد حجوزات حديثة',
                  style: AppTextStyles.font16Regular.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ),
            ),
          )
        else
          ...recentBookings.take(3).map((booking) => Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: _BookingCard(booking: booking),
          )),
      ],
    );
  }

  Widget _buildRecentReviews(BuildContext context, S s) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'أحدث التعليقات',
              style: AppTextStyles.font20Bold.copyWith(
                color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
              ),
            ),
            TextButton(
              onPressed: () {
                // Navigate to all reviews
              },
              child: Text(
                'عرض جميع التعليقات',
                style: AppTextStyles.font14Regular.copyWith(
                  color: Colors.amber,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        if (recentReviews.isEmpty)
          EnhancedCard(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Center(
                child: Text(
                  'لا توجد تعليقات حديثة',
                  style: AppTextStyles.font16Regular.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ),
            ),
          )
        else
          ...recentReviews.take(3).map((review) => Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: _ReviewCard(review: review),
          )),
      ],
    );
  }

  void _showWithdrawDialog(BuildContext context, S s) {
    final TextEditingController amountController = TextEditingController();
    String selectedMethod = 'bankTransfer';

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('سحب الأموال'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'الرصيد المتاح',
                    style: AppTextStyles.font14Regular.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  Text(
                    '\$${walletBalance.toStringAsFixed(2)}',
                    style: AppTextStyles.font20Bold.copyWith(
                      color: Colors.amber,
                    ),
                  ),
                  const SizedBox(height: 16),

                  TextField(
                    controller: amountController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'أدخل المبلغ',
                      prefixIcon: const Icon(Icons.attach_money_rounded),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),

                  const SizedBox(height: 8),
                  Text(
                    'الحد الأدنى للسحب: 50 دولار',
                    style: AppTextStyles.font12Regular.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),

                  const SizedBox(height: 16),
                  Text(
                    'طريقة السحب',
                    style: AppTextStyles.font14SemiBold.copyWith(
                      color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
                    ),
                  ),
                  const SizedBox(height: 8),

                  DropdownButtonFormField<String>(
                    value: selectedMethod,
                    decoration: InputDecoration(
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    items: const [
                      DropdownMenuItem(
                        value: 'bankTransfer',
                        child: Text('تحويل بنكي'),
                      ),
                      DropdownMenuItem(
                        value: 'paypal',
                        child: Text('باي بال'),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() {
                        selectedMethod = value!;
                      });
                    },
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(s.cancel),
                ),
                EnhancedButton(
                  text: 'سحب',
                  onPressed: () {
                    // Handle withdrawal
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم إرسال طلب السحب بنجاح'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }
}

// Stat Card Widget
class _StatCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;

  const _StatCard({
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 20,
                  ),
                ),
                const Spacer(),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              value,
              style: AppTextStyles.font18Bold.copyWith(
                color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: AppTextStyles.font12Regular.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Booking Card Widget
class _BookingCard extends StatelessWidget {
  final Map<String, dynamic> booking;

  const _BookingCard({required this.booking});

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  booking['guestName'],
                  style: AppTextStyles.font16SemiBold.copyWith(
                    color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(booking['status']).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getStatusText(booking['status'], s),
                    style: AppTextStyles.font12SemiBold.copyWith(
                      color: _getStatusColor(booking['status']),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              booking['propertyName'],
              style: AppTextStyles.font14Regular.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  Icons.calendar_today_rounded,
                  size: 16,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  '${booking['checkIn']} - ${booking['checkOut']}',
                  style: AppTextStyles.font12Regular.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                const Spacer(),
                Text(
                  '\$${booking['amount']}',
                  style: AppTextStyles.font16Bold.copyWith(
                    color: Colors.amber,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'confirmed':
        return Colors.green;
      case 'processing':
        return Colors.orange;
      case 'completed':
        return Colors.blue;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String status, S s) {
    switch (status) {
      case 'confirmed':
        return 'مؤكدة';
      case 'processing':
        return 'قيد المعالجة';
      case 'completed':
        return 'مكتملة';
      case 'cancelled':
        return 'ملغية';
      default:
        return status;
    }
  }
}

// Review Card Widget
class _ReviewCard extends StatelessWidget {
  final Map<String, dynamic> review;

  const _ReviewCard({required this.review});

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  review['guestName'],
                  style: AppTextStyles.font16SemiBold.copyWith(
                    color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
                  ),
                ),
                Row(
                  children: [
                    const Icon(
                      Icons.star_rounded,
                      color: Colors.amber,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      review['rating'].toString(),
                      style: AppTextStyles.font14SemiBold.copyWith(
                        color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              review['propertyName'],
              style: AppTextStyles.font14Regular.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              review['comment'],
              style: AppTextStyles.font14Regular.copyWith(
                color: Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            Text(
              review['date'],
              style: AppTextStyles.font12Regular.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
