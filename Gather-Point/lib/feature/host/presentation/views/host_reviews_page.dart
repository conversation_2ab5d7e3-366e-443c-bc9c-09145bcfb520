import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/core/widgets/enhanced_layouts.dart';
import 'package:gather_point/generated/l10n.dart';

class HostReviewsPage extends StatefulWidget {
  const HostReviewsPage({super.key});

  @override
  State<HostReviewsPage> createState() => _HostReviewsPageState();
}

class _HostReviewsPageState extends State<HostReviewsPage> {
  String _selectedFilter = 'all';
  
  // Dummy reviews data - will be replaced with API data later
  final List<Map<String, dynamic>> _allReviews = [
    {
      'id': '1',
      'guestName': 'فاطمة الزهراء',
      'guestImage': 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
      'propertyName': 'شقة فاخرة في الرياض',
      'propertyImage': 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=400',
      'rating': 5.0,
      'comment': 'مكان رائع ونظيف، الخدمة ممتازة والموقع مثالي. أنصح بشدة بالإقامة هنا مرة أخرى.',
      'date': '2024-01-15',
      'helpful': 12,
      'verified': true,
    },
    {
      'id': '2',
      'guestName': 'أحمد محمد',
      'guestImage': 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
      'propertyName': 'فيلا مع مسبح',
      'propertyImage': 'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=400',
      'rating': 4.5,
      'comment': 'إقامة جميلة والمسبح رائع، لكن كان هناك بعض الضوضاء من الجيران.',
      'date': '2024-01-10',
      'helpful': 8,
      'verified': true,
    },
    {
      'id': '3',
      'guestName': 'سارة أحمد',
      'guestImage': 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
      'propertyName': 'استوديو عصري',
      'propertyImage': 'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=400',
      'rating': 5.0,
      'comment': 'مكان مثالي للإقامة القصيرة، كل شيء متوفر ونظيف جداً.',
      'date': '2024-01-08',
      'helpful': 15,
      'verified': true,
    },
    {
      'id': '4',
      'guestName': 'محمد علي',
      'guestImage': 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
      'propertyName': 'شقة فاخرة في الرياض',
      'propertyImage': 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=400',
      'rating': 4.0,
      'comment': 'مكان جيد بشكل عام، لكن يحتاج لبعض التحسينات في الأثاث.',
      'date': '2024-01-05',
      'helpful': 6,
      'verified': false,
    },
    {
      'id': '5',
      'guestName': 'نورا خالد',
      'guestImage': 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150',
      'propertyName': 'فيلا مع مسبح',
      'propertyImage': 'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=400',
      'rating': 5.0,
      'comment': 'تجربة رائعة! المضيف متعاون جداً والمكان أفضل من الصور.',
      'date': '2024-01-03',
      'helpful': 20,
      'verified': true,
    },
    {
      'id': '6',
      'guestName': 'عبدالله سعد',
      'guestImage': 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150',
      'propertyName': 'استوديو عصري',
      'propertyImage': 'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=400',
      'rating': 3.5,
      'comment': 'مكان لا بأس به، لكن كان هناك مشاكل في الإنترنت.',
      'date': '2024-01-01',
      'helpful': 4,
      'verified': true,
    },
  ];

  List<Map<String, dynamic>> get _filteredReviews {
    switch (_selectedFilter) {
      case 'recent':
        return _allReviews.take(3).toList();
      case 'high_rated':
        return _allReviews.where((review) => review['rating'] >= 4.5).toList();
      case 'verified':
        return _allReviews.where((review) => review['verified'] == true).toList();
      default:
        return _allReviews;
    }
  }

  double get _averageRating {
    if (_allReviews.isEmpty) return 0.0;
    final sum = _allReviews.fold<double>(0.0, (sum, review) => sum + review['rating']);
    return sum / _allReviews.length;
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return EnhancedPageLayout(
      title: s.recentReviews,
      hasBottomNavigation: false,
      body: EnhancedScrollablePageLayout(
        hasBottomNavigation: false,
        padding: const EdgeInsets.all(16),
        children: [
          // Reviews Overview Card
          _buildOverviewCard(context, s),
          const SizedBox(height: 16),
          
          // Filter Tabs
          _buildFilterTabs(context, s),
          const SizedBox(height: 16),
          
          // Reviews List
          if (_filteredReviews.isEmpty)
            _buildEmptyState(context, s)
          else
            ..._filteredReviews.map((review) => _buildReviewCard(context, review, s)),
        ],
      ),
    );
  }

  Widget _buildOverviewCard(BuildContext context, S s) {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.amber.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.star_rounded,
                    color: Colors.amber,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        s.reviewsOverview,
                        style: AppTextStyles.font18Bold.copyWith(
                          color: context.primaryTextColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${s.totalReviews} ${_allReviews.length}',
                        style: AppTextStyles.font14Regular.copyWith(
                          color: context.secondaryTextColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  children: [
                    Text(
                      _averageRating.toStringAsFixed(1),
                      style: AppTextStyles.font24Bold.copyWith(
                        color: Colors.amber,
                      ),
                    ),
                    Row(
                      children: List.generate(5, (index) {
                        return Icon(
                          index < _averageRating.floor()
                              ? Icons.star_rounded
                              : index < _averageRating
                                  ? Icons.star_half_rounded
                                  : Icons.star_outline_rounded,
                          color: Colors.amber,
                          size: 16,
                        );
                      }),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterTabs(BuildContext context, S s) {
    final filters = [
      {'key': 'all', 'label': s.allReviews},
      {'key': 'recent', 'label': s.recentReviews},
      {'key': 'high_rated', 'label': s.highRated},
      {'key': 'verified', 'label': s.verified},
    ];

    return SizedBox(
      height: 40,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: filters.length,
        separatorBuilder: (_, __) => const SizedBox(width: 8),
        itemBuilder: (context, index) {
          final filter = filters[index];
          final isSelected = filter['key'] == _selectedFilter;
          
          return GestureDetector(
            onTap: () => setState(() => _selectedFilter = filter['key']!),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
              decoration: BoxDecoration(
                color: isSelected ? Colors.amber : Colors.transparent,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: isSelected ? Colors.amber : context.secondaryTextColor.withValues(alpha: 0.3),
                ),
              ),
              child: Text(
                filter['label']!,
                style: AppTextStyles.font14SemiBold.copyWith(
                  color: isSelected ? Colors.white : context.secondaryTextColor,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, S s) {
    return EnhancedEmptyState(
      icon: Icons.rate_review_outlined,
      title: s.noReviewsFound,
      subtitle: s.noReviewsMatchFilter,
    );
  }

  Widget _buildReviewCard(BuildContext context, Map<String, dynamic> review, S s) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: EnhancedCard(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Guest Info and Rating
              Row(
                children: [
                  CircleAvatar(
                    radius: 20,
                    backgroundImage: NetworkImage(review['guestImage']),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              review['guestName'],
                              style: AppTextStyles.font16SemiBold.copyWith(
                                color: context.primaryTextColor,
                              ),
                            ),
                            if (review['verified'])
                              Container(
                                margin: const EdgeInsets.only(right: 8),
                                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: Colors.green.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Text(
                                  s.verified,
                                  style: AppTextStyles.font10Regular.copyWith(
                                    color: Colors.green,
                                  ),
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          review['date'],
                          style: AppTextStyles.font12Regular.copyWith(
                            color: context.secondaryTextColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.amber.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.star_rounded,
                          color: Colors.amber,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          review['rating'].toString(),
                          style: AppTextStyles.font14Bold.copyWith(
                            color: Colors.amber[800],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              
              // Property Info
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: context.secondaryTextColor.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(6),
                      child: Image.network(
                        review['propertyImage'],
                        width: 40,
                        height: 40,
                        fit: BoxFit.cover,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        review['propertyName'],
                        style: AppTextStyles.font14SemiBold.copyWith(
                          color: context.primaryTextColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              
              // Review Comment
              Text(
                review['comment'],
                style: AppTextStyles.font14Regular.copyWith(
                  color: context.primaryTextColor,
                  height: 1.5,
                ),
              ),
              const SizedBox(height: 16),
              
              // Helpful Count
              Row(
                children: [
                  Icon(
                    Icons.thumb_up_outlined,
                    size: 16,
                    color: context.secondaryTextColor,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    '${review['helpful']} ${s.foundHelpful}',
                    style: AppTextStyles.font12Regular.copyWith(
                      color: context.secondaryTextColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
