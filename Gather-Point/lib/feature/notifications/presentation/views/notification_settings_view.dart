import 'package:flutter/material.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:gather_point/core/services/fcm_service.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/widgets/notification_widgets.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';

class NotificationSettingsView extends StatefulWidget {
  const NotificationSettingsView({super.key});

  @override
  State<NotificationSettingsView> createState() => _NotificationSettingsViewState();
}

class _NotificationSettingsViewState extends State<NotificationSettingsView> {
  bool _pushNotificationsEnabled = true;
  bool _eventNotificationsEnabled = true;
  bool _messageNotificationsEnabled = true;
  bool _marketingNotificationsEnabled = false;
  String? _fcmToken;
  NotificationSettings? _notificationSettings;

  @override
  void initState() {
    super.initState();
    _loadSettings();
    _loadFCMToken();
    _checkNotificationPermissions();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _pushNotificationsEnabled = prefs.getBool('push_notifications') ?? true;
      _eventNotificationsEnabled = prefs.getBool('event_notifications') ?? true;
      _messageNotificationsEnabled = prefs.getBool('message_notifications') ?? true;
      _marketingNotificationsEnabled = prefs.getBool('marketing_notifications') ?? false;
    });
  }

  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('push_notifications', _pushNotificationsEnabled);
    await prefs.setBool('event_notifications', _eventNotificationsEnabled);
    await prefs.setBool('message_notifications', _messageNotificationsEnabled);
    await prefs.setBool('marketing_notifications', _marketingNotificationsEnabled);
  }

  Future<void> _loadFCMToken() async {
    final token = await FCMService.getCurrentToken();
    setState(() {
      _fcmToken = token;
    });
  }

  Future<void> _checkNotificationPermissions() async {
    final messaging = FirebaseMessaging.instance;
    final settings = await messaging.getNotificationSettings();
    setState(() {
      _notificationSettings = settings;
    });
  }

  Future<void> _requestPermissions() async {
    final messaging = FirebaseMessaging.instance;
    final settings = await messaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
    );
    setState(() {
      _notificationSettings = settings;
    });
  }

  Future<void> _testNotification() async {
    if (_fcmToken != null) {
      // Show a test notification using the overlay
      final testNotification = const RemoteNotification(
        title: 'إشعار تجريبي',
        body: 'هذا إشعار تجريبي للتأكد من عمل النظام بشكل صحيح',
      );
      
      NotificationOverlay.show(
        context,
        testNotification,
        duration: const Duration(seconds: 3),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: AppBar(
        backgroundColor: AppColors.white,
        elevation: 0,
        title: Text(
          'إعدادات الإشعارات',
          style: GoogleFonts.inter(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppColors.black,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(
            Icons.arrow_back_ios,
            color: AppColors.black,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Permission Status Card
            _buildPermissionStatusCard(),
            const SizedBox(height: 24),
            
            // Notification Settings
            _buildSectionTitle('إعدادات الإشعارات'),
            const SizedBox(height: 16),
            _buildSettingTile(
              title: 'الإشعارات الفورية',
              subtitle: 'تفعيل أو إلغاء جميع الإشعارات',
              value: _pushNotificationsEnabled,
              onChanged: (value) {
                setState(() {
                  _pushNotificationsEnabled = value;
                });
                _saveSettings();
              },
            ),
            _buildSettingTile(
              title: 'إشعارات الأحداث',
              subtitle: 'إشعارات حول الأحداث الجديدة والتحديثات',
              value: _eventNotificationsEnabled,
              onChanged: _pushNotificationsEnabled ? (value) {
                setState(() {
                  _eventNotificationsEnabled = value;
                });
                _saveSettings();
              } : null,
            ),
            _buildSettingTile(
              title: 'إشعارات الرسائل',
              subtitle: 'إشعارات الرسائل الجديدة والمحادثات',
              value: _messageNotificationsEnabled,
              onChanged: _pushNotificationsEnabled ? (value) {
                setState(() {
                  _messageNotificationsEnabled = value;
                });
                _saveSettings();
              } : null,
            ),
            _buildSettingTile(
              title: 'الإشعارات التسويقية',
              subtitle: 'إشعارات العروض والأخبار التسويقية',
              value: _marketingNotificationsEnabled,
              onChanged: _pushNotificationsEnabled ? (value) {
                setState(() {
                  _marketingNotificationsEnabled = value;
                });
                _saveSettings();
              } : null,
            ),
            
            const SizedBox(height: 24),
            
            // Test Notification Button
            _buildTestButton(),
            
            const SizedBox(height: 24),
            
            // FCM Token Info (Debug)
            if (_fcmToken != null) _buildTokenInfo(),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionStatusCard() {
    final isAuthorized = _notificationSettings?.authorizationStatus == 
        AuthorizationStatus.authorized;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isAuthorized ? Colors.green.withOpacity(0.1) : Colors.orange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isAuthorized ? Colors.green.withOpacity(0.3) : Colors.orange.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            isAuthorized ? Icons.check_circle : Icons.warning,
            color: isAuthorized ? Colors.green : Colors.orange,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isAuthorized ? 'الإشعارات مفعلة' : 'الإشعارات غير مفعلة',
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: isAuthorized ? Colors.green : Colors.orange,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  isAuthorized 
                      ? 'يمكنك استقبال الإشعارات بشكل طبيعي'
                      : 'اضغط لتفعيل الإشعارات',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: AppColors.grey,
                  ),
                ),
              ],
            ),
          ),
          if (!isAuthorized)
            TextButton(
              onPressed: _requestPermissions,
              child: Text(
                'تفعيل',
                style: GoogleFonts.inter(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: AppColors.primary,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: GoogleFonts.inter(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: AppColors.black,
      ),
    );
  }

  Widget _buildSettingTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool>? onChanged,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.grey.withOpacity(0.2)),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: onChanged != null ? AppColors.black : AppColors.grey,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: AppColors.grey,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppColors.primary,
          ),
        ],
      ),
    );
  }

  Widget _buildTestButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _testNotification,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Text(
          'اختبار الإشعارات',
          style: GoogleFonts.inter(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppColors.white,
          ),
        ),
      ),
    );
  }

  Widget _buildTokenInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معرف الجهاز (FCM Token)',
            style: GoogleFonts.inter(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: AppColors.black,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _fcmToken!,
            style: GoogleFonts.inter(
              fontSize: 10,
              fontWeight: FontWeight.w400,
              color: AppColors.grey,
            ),
          ),
        ],
      ),
    );
  }
}
