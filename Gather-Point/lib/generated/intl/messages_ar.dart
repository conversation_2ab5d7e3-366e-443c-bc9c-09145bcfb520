// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ar locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ar';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "addImage": MessageLookupByLibrary.simpleMessage("إضافة صورة"),
        "addToFavorites":
            MessageLookupByLibrary.simpleMessage("أضف إلى المفضلة"),
        "amenities": MessageLookupByLibrary.simpleMessage("المرافق"),
        "appName": MessageLookupByLibrary.simpleMessage("نقطة التجمع"),
        "arabic": MessageLookupByLibrary.simpleMessage("العربية"),
        "availableServices":
            MessageLookupByLibrary.simpleMessage("الخدمات المتوفرة"),
        "back": MessageLookupByLibrary.simpleMessage("رجوع"),
        "bathrooms": MessageLookupByLibrary.simpleMessage("حمامات"),
        "bookNow": MessageLookupByLibrary.simpleMessage("احجز الآن"),
        "bookingDetails":
            MessageLookupByLibrary.simpleMessage("تفاصيل وسياسات الحجز"),
        "bookingPolicy": MessageLookupByLibrary.simpleMessage("سياسة الحجز"),
        "browseReels": MessageLookupByLibrary.simpleMessage("تصفح الريلز"),
        "cancel": MessageLookupByLibrary.simpleMessage("إلغاء"),
        "cancellationPolicy":
            MessageLookupByLibrary.simpleMessage("سياسة الإلغاء"),
        "commission": MessageLookupByLibrary.simpleMessage("عمولة"),
        "confirmSubmission":
            MessageLookupByLibrary.simpleMessage("تأكيد الإرسال"),
        "confirmSubmissionMessage": MessageLookupByLibrary.simpleMessage(
            "هل أنت متأكد من إرسال هذا العقار؟"),
        "createProperty": MessageLookupByLibrary.simpleMessage("إنشاء عقار"),
        "customizeExperience":
            MessageLookupByLibrary.simpleMessage("خصص تجربتك في التطبيق"),
        "dailyPrice": MessageLookupByLibrary.simpleMessage("السعر اليومي"),
        "darkMode": MessageLookupByLibrary.simpleMessage("الوضع المظلم"),
        "detectingLocation":
            MessageLookupByLibrary.simpleMessage("جارٍ تحديد المدينة..."),
        "discoverMore": MessageLookupByLibrary.simpleMessage("اكتشف المزيد"),
        "english": MessageLookupByLibrary.simpleMessage("الإنجليزية"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("أدخل السعر"),
        "error": MessageLookupByLibrary.simpleMessage("خطأ"),
        "excellent": MessageLookupByLibrary.simpleMessage("ممتاز"),
        "exploreCategories":
            MessageLookupByLibrary.simpleMessage("تصفح الأقسام"),
        "failedToCreateItem":
            MessageLookupByLibrary.simpleMessage("فشل إنشاء العنصر"),
        "failedToLoadCategories":
            MessageLookupByLibrary.simpleMessage("فشل تحميل الأقسام"),
        "failedToLoadFacilities":
            MessageLookupByLibrary.simpleMessage("فشل تحميل الخدمات"),
        "fair": MessageLookupByLibrary.simpleMessage("مقبول"),
        "featuredPlaces": MessageLookupByLibrary.simpleMessage("أماكن مميزة"),
        "filter": MessageLookupByLibrary.simpleMessage("تصفية"),
        "good": MessageLookupByLibrary.simpleMessage("جيد"),
        "guests": MessageLookupByLibrary.simpleMessage("ضيوف"),
        "home": MessageLookupByLibrary.simpleMessage("الرئيسية"),
        "imageGallery": MessageLookupByLibrary.simpleMessage("معرض الصور"),
        "language": MessageLookupByLibrary.simpleMessage("اللغة"),
        "latitude": MessageLookupByLibrary.simpleMessage("خط العرض"),
        "loading": MessageLookupByLibrary.simpleMessage("جاري التحميل..."),
        "location": MessageLookupByLibrary.simpleMessage("الموقع"),
        "locationPermissionError": MessageLookupByLibrary.simpleMessage(
            "يرجى تفعيل إذن الموقع لاستخدام التطبيق"),
        "longitude": MessageLookupByLibrary.simpleMessage("خط الطول"),
        "monthlyPrice": MessageLookupByLibrary.simpleMessage("السعر الشهري"),
        "myBookings": MessageLookupByLibrary.simpleMessage("حجوزاتي"),
        "myListings": MessageLookupByLibrary.simpleMessage("عقاراتي"),
        "nearbyPlaces": MessageLookupByLibrary.simpleMessage("الأماكن القريبة"),
        "next": MessageLookupByLibrary.simpleMessage("التالي"),
        "noResults": MessageLookupByLibrary.simpleMessage("لا توجد نتائج"),
        "numberOfBathrooms":
            MessageLookupByLibrary.simpleMessage("عدد الحمامات"),
        "numberOfBedrooms":
            MessageLookupByLibrary.simpleMessage("عدد غرف النوم"),
        "numberOfGuests": MessageLookupByLibrary.simpleMessage("عدد الضيوف"),
        "ok": MessageLookupByLibrary.simpleMessage("موافق"),
        "perNight": MessageLookupByLibrary.simpleMessage("ر.س / الليلة"),
        "pickLocation": MessageLookupByLibrary.simpleMessage("اختر الموقع"),
        "poor": MessageLookupByLibrary.simpleMessage("ضعيف"),
        "popularDestinations":
            MessageLookupByLibrary.simpleMessage("الوجهات الشائعة"),
        "pricePerNight": MessageLookupByLibrary.simpleMessage("السعر لكل ليلة"),
        "pricing": MessageLookupByLibrary.simpleMessage("الأسعار"),
        "profile": MessageLookupByLibrary.simpleMessage("حسابي"),
        "propertyCreatedSuccessfully":
            MessageLookupByLibrary.simpleMessage("تم إنشاء العقار بنجاح!"),
        "propertyDescription":
            MessageLookupByLibrary.simpleMessage("وصف العقار"),
        "propertyTitle": MessageLookupByLibrary.simpleMessage("عنوان العقار"),
        "rating": MessageLookupByLibrary.simpleMessage("التقييم"),
        "reels": MessageLookupByLibrary.simpleMessage("الريلز"),
        "reviews": MessageLookupByLibrary.simpleMessage("التقييمات"),
        "rooms": MessageLookupByLibrary.simpleMessage("غرف"),
        "search": MessageLookupByLibrary.simpleMessage("البحث"),
        "searchHint":
            MessageLookupByLibrary.simpleMessage("ابحث عن وجهتك المفضلة..."),
        "selectCategory": MessageLookupByLibrary.simpleMessage("اختر القسم"),
        "selectCity": MessageLookupByLibrary.simpleMessage("اختر المدينة"),
        "selectServices":
            MessageLookupByLibrary.simpleMessage("اختر الخدمات المتوفرة"),
        "settings": MessageLookupByLibrary.simpleMessage("الإعدادات"),
        "skip": MessageLookupByLibrary.simpleMessage("تخطي"),
        "soundClick": MessageLookupByLibrary.simpleMessage("صوت النقرات"),
        "soundClickDisabled":
            MessageLookupByLibrary.simpleMessage("تم إيقاف أصوات النقر"),
        "soundClickEnabled":
            MessageLookupByLibrary.simpleMessage("تم تفعيل أصوات النقر"),
        "soundScroll": MessageLookupByLibrary.simpleMessage("صوت التمرير"),
        "soundScrollDisabled":
            MessageLookupByLibrary.simpleMessage("تم إيقاف أصوات التمرير"),
        "soundScrollEnabled":
            MessageLookupByLibrary.simpleMessage("تم تفعيل أصوات التمرير"),
        "soundSettings": MessageLookupByLibrary.simpleMessage("إعدادات الصوت"),
        "start": MessageLookupByLibrary.simpleMessage("ابدأ"),
        "submit": MessageLookupByLibrary.simpleMessage("إرسال"),
        "tapToUploadImages":
            MessageLookupByLibrary.simpleMessage("اضغط لرفع الصور"),
        "themeDisabled":
            MessageLookupByLibrary.simpleMessage("تم تفعيل الوضع الفاتح"),
        "themeEnabled":
            MessageLookupByLibrary.simpleMessage("تم تفعيل الوضع المظلم"),
        "titleAndDescription":
            MessageLookupByLibrary.simpleMessage("العنوان والوصف"),
        "veryGood": MessageLookupByLibrary.simpleMessage("جيد جداً"),
        "viewAll": MessageLookupByLibrary.simpleMessage("عرض الكل"),
        "weeklyPrice": MessageLookupByLibrary.simpleMessage("السعر الأسبوعي"),
        "wifi": MessageLookupByLibrary.simpleMessage("واي فاي")
      };
}
