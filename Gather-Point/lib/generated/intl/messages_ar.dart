// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ar locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ar';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "about": MessageLookupByLibrary.simpleMessage("حول التطبيق"),
        "aboutThisPlace":
            MessageLookupByLibrary.simpleMessage("حول هذا المكان"),
        "active": MessageLookupByLibrary.simpleMessage("نشطة"),
        "addImage": MessageLookupByLibrary.simpleMessage("إضافة صورة"),
        "addProperty": MessageLookupByLibrary.simpleMessage("إضافة عقار"),
        "addToFavorites":
            MessageLookupByLibrary.simpleMessage("أضف إلى المفضلة"),
        "additionalInfo":
            MessageLookupByLibrary.simpleMessage("معلومات إضافية"),
        "all": MessageLookupByLibrary.simpleMessage("الكل"),
        "amenities": MessageLookupByLibrary.simpleMessage("المرافق"),
        "appDescription": MessageLookupByLibrary.simpleMessage(
            "تطبيق لحجز العقارات والأماكن"),
        "appInfo":
            MessageLookupByLibrary.simpleMessage("معلومات التطبيق والإصدار"),
        "appName": MessageLookupByLibrary.simpleMessage("نقطة التجمع"),
        "appearance": MessageLookupByLibrary.simpleMessage("المظهر"),
        "arabic": MessageLookupByLibrary.simpleMessage("العربية"),
        "availableServices":
            MessageLookupByLibrary.simpleMessage("الخدمات المتوفرة"),
        "back": MessageLookupByLibrary.simpleMessage("رجوع"),
        "backToSearch": MessageLookupByLibrary.simpleMessage("العودة للبحث"),
        "bathrooms": MessageLookupByLibrary.simpleMessage("حمامات"),
        "bedrooms": MessageLookupByLibrary.simpleMessage("غرف النوم"),
        "bio": MessageLookupByLibrary.simpleMessage("الوصف"),
        "birthdate": MessageLookupByLibrary.simpleMessage("تاريخ الميلاد"),
        "bookNow": MessageLookupByLibrary.simpleMessage("احجز الآن"),
        "bookingDetails":
            MessageLookupByLibrary.simpleMessage("تفاصيل وسياسات الحجز"),
        "bookingFee": MessageLookupByLibrary.simpleMessage("رسوم الحجز"),
        "bookingPolicy": MessageLookupByLibrary.simpleMessage("سياسة الحجز"),
        "browseReels": MessageLookupByLibrary.simpleMessage("تصفح الريلز"),
        "cancel": MessageLookupByLibrary.simpleMessage("إلغاء"),
        "cancelBooking": MessageLookupByLibrary.simpleMessage("إلغاء الحجز"),
        "cancellationPolicy":
            MessageLookupByLibrary.simpleMessage("سياسة الإلغاء"),
        "cancelled": MessageLookupByLibrary.simpleMessage("ملغية"),
        "checkConnection":
            MessageLookupByLibrary.simpleMessage("تحقق من اتصالك بالإنترنت"),
        "checkIn": MessageLookupByLibrary.simpleMessage("تسجيل الوصول"),
        "checkOut": MessageLookupByLibrary.simpleMessage("تسجيل المغادرة"),
        "commission": MessageLookupByLibrary.simpleMessage("عمولة"),
        "completed": MessageLookupByLibrary.simpleMessage("مكتملة"),
        "confirmSubmission":
            MessageLookupByLibrary.simpleMessage("تأكيد الإرسال"),
        "confirmSubmissionMessage": MessageLookupByLibrary.simpleMessage(
            "هل أنت متأكد من إرسال هذا العقار؟"),
        "confirmed": MessageLookupByLibrary.simpleMessage("مؤكدة"),
        "confirmedBookings":
            MessageLookupByLibrary.simpleMessage("الحجوزات المؤكدة"),
        "contactInfo": MessageLookupByLibrary.simpleMessage("معلومات التواصل"),
        "createProperty": MessageLookupByLibrary.simpleMessage("إنشاء عقار"),
        "customizeExperience":
            MessageLookupByLibrary.simpleMessage("خصص تجربتك في التطبيق"),
        "dailyPrice": MessageLookupByLibrary.simpleMessage("السعر اليومي"),
        "darkMode": MessageLookupByLibrary.simpleMessage("الوضع المظلم"),
        "detectingLocation":
            MessageLookupByLibrary.simpleMessage("جارٍ تحديد المدينة..."),
        "discoverMore": MessageLookupByLibrary.simpleMessage("اكتشف المزيد"),
        "editProfile":
            MessageLookupByLibrary.simpleMessage("تعديل الملف الشخصي"),
        "editProperty": MessageLookupByLibrary.simpleMessage("تعديل العقار"),
        "email": MessageLookupByLibrary.simpleMessage("البريد الإلكتروني"),
        "enableHostMode": MessageLookupByLibrary.simpleMessage(
            "قم بتفعيل وضع المستضيف لإدارة عقاراتك"),
        "english": MessageLookupByLibrary.simpleMessage("الإنجليزية"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("أدخل السعر"),
        "error": MessageLookupByLibrary.simpleMessage("خطأ"),
        "excellent": MessageLookupByLibrary.simpleMessage("ممتاز"),
        "exploreCategories":
            MessageLookupByLibrary.simpleMessage("تصفح الأقسام"),
        "exploreProperties":
            MessageLookupByLibrary.simpleMessage("استكشف العقارات"),
        "failedToCreateItem":
            MessageLookupByLibrary.simpleMessage("فشل إنشاء العنصر"),
        "failedToLoadCategories":
            MessageLookupByLibrary.simpleMessage("فشل تحميل الأقسام"),
        "failedToLoadFacilities":
            MessageLookupByLibrary.simpleMessage("فشل تحميل الخدمات"),
        "failedToLoadReels":
            MessageLookupByLibrary.simpleMessage("فشل في تحميل الريلز"),
        "fair": MessageLookupByLibrary.simpleMessage("مقبول"),
        "featuredPlaces": MessageLookupByLibrary.simpleMessage("أماكن مميزة"),
        "female": MessageLookupByLibrary.simpleMessage("أنثى"),
        "filter": MessageLookupByLibrary.simpleMessage("تصفية"),
        "fullName": MessageLookupByLibrary.simpleMessage("الاسم الكامل"),
        "gender": MessageLookupByLibrary.simpleMessage("الجنس"),
        "good": MessageLookupByLibrary.simpleMessage("جيد"),
        "guestReview": MessageLookupByLibrary.simpleMessage("تقييم الضيف"),
        "guests": MessageLookupByLibrary.simpleMessage("ضيوف"),
        "home": MessageLookupByLibrary.simpleMessage("الرئيسية"),
        "hostMode": MessageLookupByLibrary.simpleMessage("وضع المستضيف"),
        "hostedBy": MessageLookupByLibrary.simpleMessage("مستضاف من قبل"),
        "imageGallery": MessageLookupByLibrary.simpleMessage("معرض الصور"),
        "inactive": MessageLookupByLibrary.simpleMessage("غير نشطة"),
        "language": MessageLookupByLibrary.simpleMessage("اللغة"),
        "latitude": MessageLookupByLibrary.simpleMessage("خط العرض"),
        "loading": MessageLookupByLibrary.simpleMessage("جاري التحميل..."),
        "loadingReels":
            MessageLookupByLibrary.simpleMessage("جاري تحميل الريلز..."),
        "location": MessageLookupByLibrary.simpleMessage("الموقع"),
        "locationPermissionError": MessageLookupByLibrary.simpleMessage(
            "يرجى تفعيل إذن الموقع لاستخدام التطبيق"),
        "logout": MessageLookupByLibrary.simpleMessage("تسجيل الخروج"),
        "longitude": MessageLookupByLibrary.simpleMessage("خط الطول"),
        "male": MessageLookupByLibrary.simpleMessage("ذكر"),
        "manageNotifications":
            MessageLookupByLibrary.simpleMessage("إدارة إعدادات الإشعارات"),
        "mapView": MessageLookupByLibrary.simpleMessage("عرض الخريطة"),
        "monthlyPrice": MessageLookupByLibrary.simpleMessage("السعر الشهري"),
        "myBookings": MessageLookupByLibrary.simpleMessage("حجوزاتي"),
        "myListings": MessageLookupByLibrary.simpleMessage("عقاراتي"),
        "nearbyPlaces": MessageLookupByLibrary.simpleMessage("الأماكن القريبة"),
        "next": MessageLookupByLibrary.simpleMessage("التالي"),
        "noAmenitiesListed":
            MessageLookupByLibrary.simpleMessage("لا توجد مرافق مدرجة"),
        "noBookingsSubtitle":
            MessageLookupByLibrary.simpleMessage("لم تقم بأي حجوزات حتى الآن"),
        "noBookingsYet": MessageLookupByLibrary.simpleMessage("لا توجد حجوزات"),
        "noPropertiesSubtitle":
            MessageLookupByLibrary.simpleMessage("ابدأ بإضافة عقارك الأول"),
        "noPropertiesYet":
            MessageLookupByLibrary.simpleMessage("لا توجد عقارات"),
        "noResults": MessageLookupByLibrary.simpleMessage("لا توجد نتائج"),
        "notSpecified": MessageLookupByLibrary.simpleMessage("غير محدد"),
        "notifications": MessageLookupByLibrary.simpleMessage("الإشعارات"),
        "numberOfBathrooms":
            MessageLookupByLibrary.simpleMessage("عدد الحمامات"),
        "numberOfBedrooms":
            MessageLookupByLibrary.simpleMessage("عدد غرف النوم"),
        "numberOfGuests": MessageLookupByLibrary.simpleMessage("عدد الضيوف"),
        "ok": MessageLookupByLibrary.simpleMessage("موافق"),
        "pending": MessageLookupByLibrary.simpleMessage("قيد الانتظار"),
        "perNight": MessageLookupByLibrary.simpleMessage("ر.س / الليلة"),
        "personalInfo":
            MessageLookupByLibrary.simpleMessage("المعلومات الشخصية"),
        "phone": MessageLookupByLibrary.simpleMessage("رقم الجوال"),
        "pickLocation": MessageLookupByLibrary.simpleMessage("اختر الموقع"),
        "poor": MessageLookupByLibrary.simpleMessage("ضعيف"),
        "popularDestinations":
            MessageLookupByLibrary.simpleMessage("الوجهات الشائعة"),
        "pricePerNight": MessageLookupByLibrary.simpleMessage("السعر لكل ليلة"),
        "pricing": MessageLookupByLibrary.simpleMessage("الأسعار"),
        "privacy": MessageLookupByLibrary.simpleMessage("الخصوصية والأمان"),
        "privacySettings":
            MessageLookupByLibrary.simpleMessage("إعدادات الخصوصية والحماية"),
        "profile": MessageLookupByLibrary.simpleMessage("حسابي"),
        "profileImage": MessageLookupByLibrary.simpleMessage("الصورة الشخصية"),
        "propertyCreatedSuccessfully":
            MessageLookupByLibrary.simpleMessage("تم إنشاء العقار بنجاح!"),
        "propertyDescription":
            MessageLookupByLibrary.simpleMessage("وصف العقار"),
        "propertyDetails":
            MessageLookupByLibrary.simpleMessage("تفاصيل العقار"),
        "propertyTitle": MessageLookupByLibrary.simpleMessage("عنوان العقار"),
        "rating": MessageLookupByLibrary.simpleMessage("التقييم"),
        "rebookProperty": MessageLookupByLibrary.simpleMessage("إعادة الحجز"),
        "reels": MessageLookupByLibrary.simpleMessage("الريلز"),
        "reserve": MessageLookupByLibrary.simpleMessage("احجز"),
        "retry": MessageLookupByLibrary.simpleMessage("إعادة المحاولة"),
        "reviews": MessageLookupByLibrary.simpleMessage("التقييمات"),
        "rooms": MessageLookupByLibrary.simpleMessage("غرف"),
        "saveChanges": MessageLookupByLibrary.simpleMessage("حفظ التغييرات"),
        "search": MessageLookupByLibrary.simpleMessage("البحث"),
        "searchHint":
            MessageLookupByLibrary.simpleMessage("ابحث عن وجهتك المفضلة..."),
        "searching": MessageLookupByLibrary.simpleMessage("جاري البحث..."),
        "selectBirthdate":
            MessageLookupByLibrary.simpleMessage("اختر تاريخ الميلاد"),
        "selectCategory": MessageLookupByLibrary.simpleMessage("اختر القسم"),
        "selectCity": MessageLookupByLibrary.simpleMessage("اختر المدينة"),
        "selectServices":
            MessageLookupByLibrary.simpleMessage("اختر الخدمات المتوفرة"),
        "serviceFee": MessageLookupByLibrary.simpleMessage("رسوم الخدمة"),
        "settings": MessageLookupByLibrary.simpleMessage("الإعدادات"),
        "shareProperty": MessageLookupByLibrary.simpleMessage("مشاركة العقار"),
        "showAllAmenities":
            MessageLookupByLibrary.simpleMessage("عرض جميع المرافق"),
        "showLess": MessageLookupByLibrary.simpleMessage("عرض أقل"),
        "showMore": MessageLookupByLibrary.simpleMessage("عرض المزيد"),
        "skip": MessageLookupByLibrary.simpleMessage("تخطي"),
        "soundClick": MessageLookupByLibrary.simpleMessage("صوت النقرات"),
        "soundClickDisabled":
            MessageLookupByLibrary.simpleMessage("تم إيقاف أصوات النقر"),
        "soundClickEnabled":
            MessageLookupByLibrary.simpleMessage("تم تفعيل أصوات النقر"),
        "soundScroll": MessageLookupByLibrary.simpleMessage("صوت التمرير"),
        "soundScrollDisabled":
            MessageLookupByLibrary.simpleMessage("تم إيقاف أصوات التمرير"),
        "soundScrollEnabled":
            MessageLookupByLibrary.simpleMessage("تم تفعيل أصوات التمرير"),
        "soundSettings": MessageLookupByLibrary.simpleMessage("إعدادات الصوت"),
        "start": MessageLookupByLibrary.simpleMessage("ابدأ"),
        "submit": MessageLookupByLibrary.simpleMessage("إرسال"),
        "superhost": MessageLookupByLibrary.simpleMessage("مستضيف ممتاز"),
        "tapToChangeImage":
            MessageLookupByLibrary.simpleMessage("اضغط لتغيير الصورة"),
        "tapToUploadImages":
            MessageLookupByLibrary.simpleMessage("اضغط لرفع الصور"),
        "taxes": MessageLookupByLibrary.simpleMessage("الضرائب"),
        "themeDisabled":
            MessageLookupByLibrary.simpleMessage("تم تفعيل الوضع الفاتح"),
        "themeEnabled":
            MessageLookupByLibrary.simpleMessage("تم تفعيل الوضع المظلم"),
        "titleAndDescription":
            MessageLookupByLibrary.simpleMessage("العنوان والوصف"),
        "total": MessageLookupByLibrary.simpleMessage("الإجمالي"),
        "totalBookings":
            MessageLookupByLibrary.simpleMessage("إجمالي الحجوزات"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("إجمالي السعر"),
        "totalProperties":
            MessageLookupByLibrary.simpleMessage("إجمالي العقارات"),
        "totalReservations":
            MessageLookupByLibrary.simpleMessage("إجمالي الحجوزات"),
        "totalViews": MessageLookupByLibrary.simpleMessage("إجمالي المشاهدات"),
        "tryDifferentKeywords":
            MessageLookupByLibrary.simpleMessage("جرب البحث بكلمات مختلفة"),
        "underReview": MessageLookupByLibrary.simpleMessage("قيد المراجعة"),
        "version": MessageLookupByLibrary.simpleMessage("الإصدار"),
        "veryGood": MessageLookupByLibrary.simpleMessage("جيد جداً"),
        "viewAll": MessageLookupByLibrary.simpleMessage("عرض الكل"),
        "viewDetails": MessageLookupByLibrary.simpleMessage("عرض التفاصيل"),
        "weeklyPrice": MessageLookupByLibrary.simpleMessage("السعر الأسبوعي"),
        "whatThisPlaceOffers":
            MessageLookupByLibrary.simpleMessage("ما يقدمه هذا المكان"),
        "whereYoullBe": MessageLookupByLibrary.simpleMessage("أين ستكون"),
        "wifi": MessageLookupByLibrary.simpleMessage("واي فاي"),
        "yearsHosting": MessageLookupByLibrary.simpleMessage("سنوات الاستضافة")
      };
}
