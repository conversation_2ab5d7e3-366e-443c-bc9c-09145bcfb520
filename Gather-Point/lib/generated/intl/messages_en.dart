// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "about": MessageLookupByLibrary.simpleMessage("About App"),
        "aboutThisPlace":
            MessageLookupByLibrary.simpleMessage("About this place"),
        "active": MessageLookupByLibrary.simpleMessage("Active"),
        "addImage": MessageLookupByLibrary.simpleMessage("Add Image"),
        "addProperty": MessageLookupByLibrary.simpleMessage("Add Property"),
        "addToFavorites":
            MessageLookupByLibrary.simpleMessage("Add to favorites"),
        "additionalInfo":
            MessageLookupByLibrary.simpleMessage("Additional Information"),
        "all": MessageLookupByLibrary.simpleMessage("All"),
        "amenities": MessageLookupByLibrary.simpleMessage("Amenities"),
        "appDescription": MessageLookupByLibrary.simpleMessage(
            "Property booking and rental app"),
        "appInfo":
            MessageLookupByLibrary.simpleMessage("App information and version"),
        "appName": MessageLookupByLibrary.simpleMessage("Gather Point"),
        "appearance": MessageLookupByLibrary.simpleMessage("Appearance"),
        "arabic": MessageLookupByLibrary.simpleMessage("Arabic"),
        "availableServices":
            MessageLookupByLibrary.simpleMessage("Available Services"),
        "back": MessageLookupByLibrary.simpleMessage("Back"),
        "backToSearch": MessageLookupByLibrary.simpleMessage("Back to Search"),
        "bathrooms": MessageLookupByLibrary.simpleMessage("Bathrooms"),
        "bedrooms": MessageLookupByLibrary.simpleMessage("Bedrooms"),
        "bio": MessageLookupByLibrary.simpleMessage("Bio"),
        "birthdate": MessageLookupByLibrary.simpleMessage("Birthdate"),
        "bookNow": MessageLookupByLibrary.simpleMessage("Book Now"),
        "bookingDetails":
            MessageLookupByLibrary.simpleMessage("Booking Details & Policies"),
        "bookingFee": MessageLookupByLibrary.simpleMessage("Booking fee"),
        "bookingPolicy": MessageLookupByLibrary.simpleMessage("Booking Policy"),
        "browseReels": MessageLookupByLibrary.simpleMessage("Browse Reels"),
        "cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
        "cancelBooking": MessageLookupByLibrary.simpleMessage("Cancel Booking"),
        "cancellationPolicy":
            MessageLookupByLibrary.simpleMessage("Cancellation Policy"),
        "cancelled": MessageLookupByLibrary.simpleMessage("Cancelled"),
        "checkConnection": MessageLookupByLibrary.simpleMessage(
            "Check your internet connection"),
        "checkIn": MessageLookupByLibrary.simpleMessage("Check In"),
        "checkOut": MessageLookupByLibrary.simpleMessage("Check Out"),
        "commission": MessageLookupByLibrary.simpleMessage("Commission"),
        "completed": MessageLookupByLibrary.simpleMessage("Completed"),
        "confirmSubmission":
            MessageLookupByLibrary.simpleMessage("Confirm Submission"),
        "confirmSubmissionMessage": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to submit this property?"),
        "confirmed": MessageLookupByLibrary.simpleMessage("Confirmed"),
        "confirmedBookings":
            MessageLookupByLibrary.simpleMessage("Confirmed Bookings"),
        "contactInfo":
            MessageLookupByLibrary.simpleMessage("Contact Information"),
        "createProperty":
            MessageLookupByLibrary.simpleMessage("Create Property"),
        "customizeExperience": MessageLookupByLibrary.simpleMessage(
            "Customize your app experience"),
        "dailyPrice": MessageLookupByLibrary.simpleMessage("Daily Price"),
        "darkMode": MessageLookupByLibrary.simpleMessage("Dark Mode"),
        "detectingLocation":
            MessageLookupByLibrary.simpleMessage("Detecting location..."),
        "discoverMore": MessageLookupByLibrary.simpleMessage("Discover More"),
        "editProfile": MessageLookupByLibrary.simpleMessage("Edit Profile"),
        "editProperty": MessageLookupByLibrary.simpleMessage("Edit Property"),
        "email": MessageLookupByLibrary.simpleMessage("Email"),
        "enableHostMode": MessageLookupByLibrary.simpleMessage(
            "Enable host mode to manage your properties"),
        "english": MessageLookupByLibrary.simpleMessage("English"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("Enter price"),
        "error": MessageLookupByLibrary.simpleMessage("Error"),
        "excellent": MessageLookupByLibrary.simpleMessage("Excellent"),
        "exploreCategories":
            MessageLookupByLibrary.simpleMessage("Explore Categories"),
        "exploreProperties":
            MessageLookupByLibrary.simpleMessage("Explore Properties"),
        "failedToCreateItem":
            MessageLookupByLibrary.simpleMessage("Failed to create item"),
        "failedToLoadCategories":
            MessageLookupByLibrary.simpleMessage("Failed to load categories"),
        "failedToLoadFacilities":
            MessageLookupByLibrary.simpleMessage("Failed to load facilities"),
        "failedToLoadReels":
            MessageLookupByLibrary.simpleMessage("Failed to load reels"),
        "fair": MessageLookupByLibrary.simpleMessage("Fair"),
        "featuredPlaces":
            MessageLookupByLibrary.simpleMessage("Featured Places"),
        "female": MessageLookupByLibrary.simpleMessage("Female"),
        "filter": MessageLookupByLibrary.simpleMessage("Filter"),
        "fullName": MessageLookupByLibrary.simpleMessage("Full Name"),
        "gender": MessageLookupByLibrary.simpleMessage("Gender"),
        "good": MessageLookupByLibrary.simpleMessage("Good"),
        "guestReview": MessageLookupByLibrary.simpleMessage("Guest Review"),
        "guests": MessageLookupByLibrary.simpleMessage("Guests"),
        "home": MessageLookupByLibrary.simpleMessage("Home"),
        "hostMode": MessageLookupByLibrary.simpleMessage("Host Mode"),
        "hostedBy": MessageLookupByLibrary.simpleMessage("Hosted by"),
        "imageGallery": MessageLookupByLibrary.simpleMessage("Image Gallery"),
        "inactive": MessageLookupByLibrary.simpleMessage("Inactive"),
        "language": MessageLookupByLibrary.simpleMessage("Language"),
        "latitude": MessageLookupByLibrary.simpleMessage("Latitude"),
        "loading": MessageLookupByLibrary.simpleMessage("Loading..."),
        "loadingReels":
            MessageLookupByLibrary.simpleMessage("Loading reels..."),
        "location": MessageLookupByLibrary.simpleMessage("Location"),
        "locationPermissionError": MessageLookupByLibrary.simpleMessage(
            "Please enable location permission to use the app"),
        "logout": MessageLookupByLibrary.simpleMessage("Logout"),
        "longitude": MessageLookupByLibrary.simpleMessage("Longitude"),
        "male": MessageLookupByLibrary.simpleMessage("Male"),
        "manageNotifications": MessageLookupByLibrary.simpleMessage(
            "Manage notification settings"),
        "mapView": MessageLookupByLibrary.simpleMessage("Map view"),
        "monthlyPrice": MessageLookupByLibrary.simpleMessage("Monthly Price"),
        "myBookings": MessageLookupByLibrary.simpleMessage("My Bookings"),
        "myListings": MessageLookupByLibrary.simpleMessage("My Listings"),
        "nearbyPlaces": MessageLookupByLibrary.simpleMessage("Nearby Places"),
        "next": MessageLookupByLibrary.simpleMessage("Next"),
        "noAmenitiesListed":
            MessageLookupByLibrary.simpleMessage("No amenities listed"),
        "noBookingsSubtitle": MessageLookupByLibrary.simpleMessage(
            "You haven\'t made any bookings yet"),
        "noBookingsYet":
            MessageLookupByLibrary.simpleMessage("No bookings yet"),
        "noPropertiesSubtitle": MessageLookupByLibrary.simpleMessage(
            "Start by adding your first property"),
        "noPropertiesYet":
            MessageLookupByLibrary.simpleMessage("No properties yet"),
        "noResults": MessageLookupByLibrary.simpleMessage("No results found"),
        "notSpecified": MessageLookupByLibrary.simpleMessage("Not Specified"),
        "notifications": MessageLookupByLibrary.simpleMessage("Notifications"),
        "numberOfBathrooms":
            MessageLookupByLibrary.simpleMessage("Number of Bathrooms"),
        "numberOfBedrooms":
            MessageLookupByLibrary.simpleMessage("Number of Bedrooms"),
        "numberOfGuests":
            MessageLookupByLibrary.simpleMessage("Number of Guests"),
        "ok": MessageLookupByLibrary.simpleMessage("OK"),
        "pending": MessageLookupByLibrary.simpleMessage("Pending"),
        "perNight": MessageLookupByLibrary.simpleMessage("SAR / night"),
        "personalInfo":
            MessageLookupByLibrary.simpleMessage("Personal Information"),
        "phone": MessageLookupByLibrary.simpleMessage("Phone"),
        "pickLocation": MessageLookupByLibrary.simpleMessage("Pick Location"),
        "poor": MessageLookupByLibrary.simpleMessage("Poor"),
        "popularDestinations":
            MessageLookupByLibrary.simpleMessage("Popular Destinations"),
        "pricePerNight":
            MessageLookupByLibrary.simpleMessage("Price per night"),
        "pricing": MessageLookupByLibrary.simpleMessage("Pricing"),
        "privacy": MessageLookupByLibrary.simpleMessage("Privacy & Security"),
        "privacySettings": MessageLookupByLibrary.simpleMessage(
            "Privacy and security settings"),
        "profile": MessageLookupByLibrary.simpleMessage("Profile"),
        "profileImage": MessageLookupByLibrary.simpleMessage("Profile Image"),
        "propertyCreatedSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Property created successfully!"),
        "propertyDescription":
            MessageLookupByLibrary.simpleMessage("Property Description"),
        "propertyDetails":
            MessageLookupByLibrary.simpleMessage("Property Details"),
        "propertyTitle": MessageLookupByLibrary.simpleMessage("Property Title"),
        "rating": MessageLookupByLibrary.simpleMessage("Rating"),
        "rebookProperty":
            MessageLookupByLibrary.simpleMessage("Rebook Property"),
        "reels": MessageLookupByLibrary.simpleMessage("Reels"),
        "reserve": MessageLookupByLibrary.simpleMessage("Reserve"),
        "retry": MessageLookupByLibrary.simpleMessage("Retry"),
        "reviews": MessageLookupByLibrary.simpleMessage("Reviews"),
        "rooms": MessageLookupByLibrary.simpleMessage("Rooms"),
        "saveChanges": MessageLookupByLibrary.simpleMessage("Save Changes"),
        "search": MessageLookupByLibrary.simpleMessage("Search"),
        "searchHint": MessageLookupByLibrary.simpleMessage(
            "Search your favorite destination..."),
        "searching": MessageLookupByLibrary.simpleMessage("Searching..."),
        "selectBirthdate":
            MessageLookupByLibrary.simpleMessage("Select Birthdate"),
        "selectCategory":
            MessageLookupByLibrary.simpleMessage("Select Category"),
        "selectCity": MessageLookupByLibrary.simpleMessage("Select City"),
        "selectServices":
            MessageLookupByLibrary.simpleMessage("Select available services"),
        "serviceFee": MessageLookupByLibrary.simpleMessage("Service fee"),
        "settings": MessageLookupByLibrary.simpleMessage("Settings"),
        "shareProperty": MessageLookupByLibrary.simpleMessage("Share Property"),
        "showAllAmenities":
            MessageLookupByLibrary.simpleMessage("Show all amenities"),
        "showLess": MessageLookupByLibrary.simpleMessage("Show less"),
        "showMore": MessageLookupByLibrary.simpleMessage("Show more"),
        "skip": MessageLookupByLibrary.simpleMessage("Skip"),
        "soundClick": MessageLookupByLibrary.simpleMessage("Click Sounds"),
        "soundClickDisabled":
            MessageLookupByLibrary.simpleMessage("Click sounds disabled"),
        "soundClickEnabled":
            MessageLookupByLibrary.simpleMessage("Click sounds enabled"),
        "soundScroll": MessageLookupByLibrary.simpleMessage("Scroll Sounds"),
        "soundScrollDisabled":
            MessageLookupByLibrary.simpleMessage("Scroll sounds disabled"),
        "soundScrollEnabled":
            MessageLookupByLibrary.simpleMessage("Scroll sounds enabled"),
        "soundSettings": MessageLookupByLibrary.simpleMessage("Sound Settings"),
        "start": MessageLookupByLibrary.simpleMessage("Start"),
        "submit": MessageLookupByLibrary.simpleMessage("Submit"),
        "superhost": MessageLookupByLibrary.simpleMessage("Superhost"),
        "tapToChangeImage":
            MessageLookupByLibrary.simpleMessage("Tap to change image"),
        "tapToUploadImages":
            MessageLookupByLibrary.simpleMessage("Tap to upload images"),
        "taxes": MessageLookupByLibrary.simpleMessage("Taxes"),
        "themeDisabled":
            MessageLookupByLibrary.simpleMessage("Light theme enabled"),
        "themeEnabled":
            MessageLookupByLibrary.simpleMessage("Dark theme enabled"),
        "titleAndDescription":
            MessageLookupByLibrary.simpleMessage("Title & Description"),
        "total": MessageLookupByLibrary.simpleMessage("Total"),
        "totalBookings": MessageLookupByLibrary.simpleMessage("Total Bookings"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Total Price"),
        "totalProperties":
            MessageLookupByLibrary.simpleMessage("Total Properties"),
        "totalReservations":
            MessageLookupByLibrary.simpleMessage("Total Reservations"),
        "totalViews": MessageLookupByLibrary.simpleMessage("Total Views"),
        "tryDifferentKeywords": MessageLookupByLibrary.simpleMessage(
            "Try searching with different keywords"),
        "underReview": MessageLookupByLibrary.simpleMessage("Under Review"),
        "version": MessageLookupByLibrary.simpleMessage("Version"),
        "veryGood": MessageLookupByLibrary.simpleMessage("Very Good"),
        "viewAll": MessageLookupByLibrary.simpleMessage("View All"),
        "viewDetails": MessageLookupByLibrary.simpleMessage("View Details"),
        "weeklyPrice": MessageLookupByLibrary.simpleMessage("Weekly Price"),
        "whatThisPlaceOffers":
            MessageLookupByLibrary.simpleMessage("What this place offers"),
        "whereYoullBe":
            MessageLookupByLibrary.simpleMessage("Where you\'ll be"),
        "wifi": MessageLookupByLibrary.simpleMessage("Wi-Fi"),
        "yearsHosting": MessageLookupByLibrary.simpleMessage("years hosting")
      };
}
