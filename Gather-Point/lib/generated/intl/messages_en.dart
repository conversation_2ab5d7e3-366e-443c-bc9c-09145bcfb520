// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "addImage": MessageLookupByLibrary.simpleMessage("Add Image"),
        "addToFavorites":
            MessageLookupByLibrary.simpleMessage("Add to favorites"),
        "amenities": MessageLookupByLibrary.simpleMessage("Amenities"),
        "appName": MessageLookupByLibrary.simpleMessage("Gather Point"),
        "arabic": MessageLookupByLibrary.simpleMessage("Arabic"),
        "availableServices":
            MessageLookupByLibrary.simpleMessage("Available Services"),
        "back": MessageLookupByLibrary.simpleMessage("Back"),
        "bathrooms": MessageLookupByLibrary.simpleMessage("Bathrooms"),
        "bookNow": MessageLookupByLibrary.simpleMessage("Book Now"),
        "bookingDetails":
            MessageLookupByLibrary.simpleMessage("Booking Details & Policies"),
        "bookingPolicy": MessageLookupByLibrary.simpleMessage("Booking Policy"),
        "browseReels": MessageLookupByLibrary.simpleMessage("Browse Reels"),
        "cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
        "cancellationPolicy":
            MessageLookupByLibrary.simpleMessage("Cancellation Policy"),
        "commission": MessageLookupByLibrary.simpleMessage("Commission"),
        "confirmSubmission":
            MessageLookupByLibrary.simpleMessage("Confirm Submission"),
        "confirmSubmissionMessage": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to submit this property?"),
        "createProperty":
            MessageLookupByLibrary.simpleMessage("Create Property"),
        "customizeExperience": MessageLookupByLibrary.simpleMessage(
            "Customize your app experience"),
        "dailyPrice": MessageLookupByLibrary.simpleMessage("Daily Price"),
        "darkMode": MessageLookupByLibrary.simpleMessage("Dark Mode"),
        "detectingLocation":
            MessageLookupByLibrary.simpleMessage("Detecting location..."),
        "discoverMore": MessageLookupByLibrary.simpleMessage("Discover More"),
        "english": MessageLookupByLibrary.simpleMessage("English"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("Enter price"),
        "error": MessageLookupByLibrary.simpleMessage("Error"),
        "excellent": MessageLookupByLibrary.simpleMessage("Excellent"),
        "exploreCategories":
            MessageLookupByLibrary.simpleMessage("Explore Categories"),
        "failedToCreateItem":
            MessageLookupByLibrary.simpleMessage("Failed to create item"),
        "failedToLoadCategories":
            MessageLookupByLibrary.simpleMessage("Failed to load categories"),
        "failedToLoadFacilities":
            MessageLookupByLibrary.simpleMessage("Failed to load facilities"),
        "fair": MessageLookupByLibrary.simpleMessage("Fair"),
        "featuredPlaces":
            MessageLookupByLibrary.simpleMessage("Featured Places"),
        "filter": MessageLookupByLibrary.simpleMessage("Filter"),
        "good": MessageLookupByLibrary.simpleMessage("Good"),
        "guests": MessageLookupByLibrary.simpleMessage("Guests"),
        "home": MessageLookupByLibrary.simpleMessage("Home"),
        "imageGallery": MessageLookupByLibrary.simpleMessage("Image Gallery"),
        "language": MessageLookupByLibrary.simpleMessage("Language"),
        "latitude": MessageLookupByLibrary.simpleMessage("Latitude"),
        "loading": MessageLookupByLibrary.simpleMessage("Loading..."),
        "location": MessageLookupByLibrary.simpleMessage("Location"),
        "locationPermissionError": MessageLookupByLibrary.simpleMessage(
            "Please enable location permission to use the app"),
        "longitude": MessageLookupByLibrary.simpleMessage("Longitude"),
        "monthlyPrice": MessageLookupByLibrary.simpleMessage("Monthly Price"),
        "myBookings": MessageLookupByLibrary.simpleMessage("My Bookings"),
        "myListings": MessageLookupByLibrary.simpleMessage("My Listings"),
        "nearbyPlaces": MessageLookupByLibrary.simpleMessage("Nearby Places"),
        "next": MessageLookupByLibrary.simpleMessage("Next"),
        "noResults": MessageLookupByLibrary.simpleMessage("No results found"),
        "numberOfBathrooms":
            MessageLookupByLibrary.simpleMessage("Number of Bathrooms"),
        "numberOfBedrooms":
            MessageLookupByLibrary.simpleMessage("Number of Bedrooms"),
        "numberOfGuests":
            MessageLookupByLibrary.simpleMessage("Number of Guests"),
        "ok": MessageLookupByLibrary.simpleMessage("OK"),
        "perNight": MessageLookupByLibrary.simpleMessage("SAR / night"),
        "pickLocation": MessageLookupByLibrary.simpleMessage("Pick Location"),
        "poor": MessageLookupByLibrary.simpleMessage("Poor"),
        "popularDestinations":
            MessageLookupByLibrary.simpleMessage("Popular Destinations"),
        "pricePerNight":
            MessageLookupByLibrary.simpleMessage("Price per night"),
        "pricing": MessageLookupByLibrary.simpleMessage("Pricing"),
        "profile": MessageLookupByLibrary.simpleMessage("Profile"),
        "propertyCreatedSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Property created successfully!"),
        "propertyDescription":
            MessageLookupByLibrary.simpleMessage("Property Description"),
        "propertyTitle": MessageLookupByLibrary.simpleMessage("Property Title"),
        "rating": MessageLookupByLibrary.simpleMessage("Rating"),
        "reels": MessageLookupByLibrary.simpleMessage("Reels"),
        "reviews": MessageLookupByLibrary.simpleMessage("Reviews"),
        "rooms": MessageLookupByLibrary.simpleMessage("Rooms"),
        "search": MessageLookupByLibrary.simpleMessage("Search"),
        "searchHint": MessageLookupByLibrary.simpleMessage(
            "Search your favorite destination..."),
        "selectCategory":
            MessageLookupByLibrary.simpleMessage("Select Category"),
        "selectCity": MessageLookupByLibrary.simpleMessage("Select City"),
        "selectServices":
            MessageLookupByLibrary.simpleMessage("Select available services"),
        "settings": MessageLookupByLibrary.simpleMessage("Settings"),
        "skip": MessageLookupByLibrary.simpleMessage("Skip"),
        "soundClick": MessageLookupByLibrary.simpleMessage("Click Sounds"),
        "soundClickDisabled":
            MessageLookupByLibrary.simpleMessage("Click sounds disabled"),
        "soundClickEnabled":
            MessageLookupByLibrary.simpleMessage("Click sounds enabled"),
        "soundScroll": MessageLookupByLibrary.simpleMessage("Scroll Sounds"),
        "soundScrollDisabled":
            MessageLookupByLibrary.simpleMessage("Scroll sounds disabled"),
        "soundScrollEnabled":
            MessageLookupByLibrary.simpleMessage("Scroll sounds enabled"),
        "soundSettings": MessageLookupByLibrary.simpleMessage("Sound Settings"),
        "start": MessageLookupByLibrary.simpleMessage("Start"),
        "submit": MessageLookupByLibrary.simpleMessage("Submit"),
        "tapToUploadImages":
            MessageLookupByLibrary.simpleMessage("Tap to upload images"),
        "themeDisabled":
            MessageLookupByLibrary.simpleMessage("Light theme enabled"),
        "themeEnabled":
            MessageLookupByLibrary.simpleMessage("Dark theme enabled"),
        "titleAndDescription":
            MessageLookupByLibrary.simpleMessage("Title & Description"),
        "veryGood": MessageLookupByLibrary.simpleMessage("Very Good"),
        "viewAll": MessageLookupByLibrary.simpleMessage("View All"),
        "weeklyPrice": MessageLookupByLibrary.simpleMessage("Weekly Price"),
        "wifi": MessageLookupByLibrary.simpleMessage("Wi-Fi")
      };
}
