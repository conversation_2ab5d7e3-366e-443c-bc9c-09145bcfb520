// ignore_for_file: prefer_const_constructors, use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gather_point/core/navigation_bar/custom_nav_bar.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/utils/app_assets.dart';
import 'package:gather_point/core/utils/app_constants.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:go_router/go_router.dart';
import 'package:hive/hive.dart';
import 'package:persistent_bottom_nav_bar_v2/persistent_bottom_nav_bar_v2.dart';

class MainNavigationBar extends StatelessWidget {
  const MainNavigationBar({super.key, required this.navigationShell});

  final StatefulNavigationShell navigationShell;

  @override
  Widget build(BuildContext context) {
    final userBox = Hive.box<UserEntity>(AppConstants.kMyProfileBoxName);
    final isHosterMode =
        userBox.get(AppConstants.kMyProfileKey)?.isHosterMode ?? false;

    // Get translations
    final s = S.of(context);

    // Get theme colors
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return PersistentTabView.router(
      navBarHeight: 64, // Optimized height to prevent overflow
      tabs: isHosterMode
          ? [
              PersistentRouterTabConfig(
                item: ItemConfig(
                  icon: ColorFiltered(
                    colorFilter: ColorFilter.mode(
                      context.accentColor,
                      BlendMode.srcIn,
                    ),
                    child: SvgPicture.asset(
                      AppAssets.iconsHome,
                      width: 24,
                      height: 24,
                    ),
                  ),
                  title: s.home,
                ),
              ),
              PersistentRouterTabConfig(
                item: ItemConfig(
                  icon: ColorFiltered(
                    colorFilter: ColorFilter.mode(
                      context.accentColor,
                      BlendMode.srcIn,
                    ),
                    child: SvgPicture.asset(
                      AppAssets.iconsCalendar,
                      width: 24,
                      height: 24,
                    ),
                  ),
                  title: s.myBookings,
                ),
              ),
              PersistentRouterTabConfig(
                item: ItemConfig(
                  icon: ColorFiltered(
                    colorFilter: ColorFilter.mode(
                      context.accentColor,
                      BlendMode.srcIn,
                    ),
                    child: SvgPicture.asset(
                      AppAssets.iconsSearch,
                      width: 24,
                      height: 24,
                    ),
                  ),
                  title: s.myListings,
                ),
              ),
              PersistentRouterTabConfig(
                item: ItemConfig(
                  icon: ColorFiltered(
                    colorFilter: ColorFilter.mode(
                      context.accentColor,
                      BlendMode.srcIn,
                    ),
                    child: SvgPicture.asset(
                      AppAssets.iconsProfile,
                      width: 24,
                      height: 24,
                    ),
                  ),
                  title: s.profile,
                ),
              ),
            ]
          : [
              PersistentRouterTabConfig(
                item: ItemConfig(
                  icon: ColorFiltered(
                    colorFilter: ColorFilter.mode(
                      context.accentColor,
                      BlendMode.srcIn,
                    ),
                    child: SvgPicture.asset(
                      AppAssets.iconsHome,
                      width: 24,
                      height: 24,
                    ),
                  ),
                  title: s.home,
                ),
              ),
              PersistentRouterTabConfig(
                item: ItemConfig(
                  icon: ColorFiltered(
                    colorFilter: ColorFilter.mode(
                      context.accentColor,
                      BlendMode.srcIn,
                    ),
                    child: SvgPicture.asset(
                      AppAssets.iconsSearch,
                      width: 24,
                      height: 24,
                    ),
                  ),
                  title: s.search,
                ),
              ),
              PersistentRouterTabConfig(
                item: ItemConfig(
                  icon: ColorFiltered(
                    colorFilter: ColorFilter.mode(
                      context.accentColor,
                      BlendMode.srcIn,
                    ),
                    child: SvgPicture.asset(
                      AppAssets.iconsReels,
                      width: 25,
                      height: 25,
                    ),
                  ),
                  title: s.reels,
                ),
              ),
              PersistentRouterTabConfig(
                item: ItemConfig(
                  icon: ColorFiltered(
                    colorFilter: ColorFilter.mode(
                      context.accentColor,
                      BlendMode.srcIn,
                    ),
                    child: SvgPicture.asset(
                      AppAssets.iconsProfile,
                      width: 24,
                      height: 24,
                    ),
                  ),
                  title: s.profile,
                ),
              ),
            ],
      navBarBuilder: (navBarConfig) => CustomNavBar(
        navBarConfig: navBarConfig,
        navBarDecoration: NavBarDecoration(
          padding: EdgeInsets.zero,
          color: context.backgroundColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          boxShadow: [
            BoxShadow(
              color: isDark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.grey.withValues(alpha: 0.2),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
      ),
      navigationShell: navigationShell,
    );
  }
}
