// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/utils/sound_manager.dart';
import 'package:persistent_bottom_nav_bar_v2/persistent_bottom_nav_bar_v2.dart';

class CustomNavBar extends StatelessWidget {
  final NavBarConfig navBarConfig;
  final NavBarDecoration navBarDecoration;

  const CustomNavBar({
    super.key,
    required this.navBarConfig,
    this.navBarDecoration = const NavBarDecoration(),
  });

  Widget _buildItem(ItemConfig item, bool isSelected, BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      height: 50, // Fixed height to prevent any overflow
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          // Icon with enhanced styling - larger and centered
          Expanded(
            flex: 3,
            child: Center(
              child: Container(
                width: 24,
                height: 24,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: isSelected
                      ? theme.colorScheme.primary.withValues(alpha: 0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: ColorFiltered(
                  colorFilter: ColorFilter.mode(
                    isSelected
                        ? theme.colorScheme.primary
                        : (isDark ? Colors.grey[400]! : Colors.grey[600]!),
                    BlendMode.srcIn,
                  ),
                  child: SizedBox(
                    width: 24,
                    height: 24,
                    child: FittedBox(
                      fit: BoxFit.contain,
                      alignment: Alignment.center,
                      child: item.icon,
                    ),
                  ),
                ),
              ),
            ),
          ),
          SizedBox(height: 4),
          // Text with better typography
          Expanded(
            flex: 2,
            child: Center(
              child: FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  item.title!,
                  style: AppTextStyles.font12Regular.copyWith(
                    color: isSelected
                        ? theme.colorScheme.primary
                        : (isDark ? Colors.grey[400] : Colors.grey[600]),
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                    fontSize: 12,
                    height: 1.1,
                    letterSpacing: 0.2,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return DecoratedNavBar(
      decoration: navBarDecoration,
      height: navBarConfig.navBarHeight,
      child: Container(
        decoration: BoxDecoration(
          color: theme.scaffoldBackgroundColor,
          border: Border(
            top: BorderSide(
              color: isDark ? Colors.grey[800]! : Colors.grey[200]!,
              width: 0.5,
            ),
          ),
        ),
        child: SafeArea(
          minimum: const EdgeInsets.only(bottom: 2),
          child: Container(
            height: 64, // Fixed height matching item height + padding
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: navBarConfig.items.map((item) {
                int index = navBarConfig.items.indexOf(item);
                return Expanded(
                  child: InkWell(
                    onTap: () async {
                      try {
                        // Check if context is still valid
                        if (!context.mounted) {
                          debugPrint(
                              'Context not mounted, skipping navigation');
                          return;
                        }

                        debugPrint(
                            'Navigation attempt: index=$index, mounted=${context.mounted}');

                        // Play sound first (safer operation)
                        try {
                          SoundManager.playClickSound();
                        } catch (soundError) {
                          debugPrint('Sound error: $soundError');
                        }

                        // Add a small delay to ensure context stability
                        await Future.delayed(const Duration(milliseconds: 50));

                        // Double-check context is still valid after delay
                        if (!context.mounted) {
                          debugPrint('Context became unmounted during delay');
                          return;
                        }

                        debugPrint(
                            'About to call onItemSelected with index: $index');

                        // Perform navigation with additional safety
                        navBarConfig.onItemSelected(index);

                        debugPrint('Navigation completed successfully');
                      } catch (e, stackTrace) {
                        debugPrint('Navigation error: $e');
                        debugPrint('Stack trace: $stackTrace');
                        // Try to show a user-friendly message if context is still valid
                        if (context.mounted) {
                          try {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  'حدث خطأ في التنقل، يرجى المحاولة مرة أخرى',
                                  style: AppTextStyles.font14Regular.copyWith(
                                    color: Colors.white,
                                  ),
                                ),
                                backgroundColor: theme.colorScheme.error,
                                duration: const Duration(seconds: 2),
                              ),
                            );
                          } catch (snackBarError) {
                            debugPrint(
                                'Could not show snackbar: $snackBarError');
                          }
                        }
                      }
                    },
                    borderRadius: BorderRadius.circular(12),
                    child: _buildItem(
                      item,
                      navBarConfig.selectedIndex == index,
                      context,
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ),
      ),
    );
  }
}
