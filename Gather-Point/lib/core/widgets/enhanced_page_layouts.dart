import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/widgets/enhanced_layouts.dart';
import 'package:gather_point/generated/l10n.dart';

/// Enhanced Page Layout with 70px bottom navigation consideration
class EnhancedPageLayout extends StatelessWidget {
  final String? title;
  final Widget body;
  final List<Widget>? actions;
  final Widget? floatingActionButton;
  final bool showAppBar;
  final bool showBackButton;
  final bool isLoading;
  final String? errorMessage;
  final VoidCallback? onRetry;
  final EdgeInsetsGeometry? padding;
  final bool hasBottomNavigation;
  final Color? backgroundColor;
  final Widget? bottomSheet;

  const EnhancedPageLayout({
    super.key,
    this.title,
    required this.body,
    this.actions,
    this.floatingActionButton,
    this.showAppBar = true,
    this.showBackButton = true,
    this.isLoading = false,
    this.errorMessage,
    this.onRetry,
    this.padding,
    this.hasBottomNavigation = true, // Default true for main app pages
    this.backgroundColor,
    this.bottomSheet,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: isDark 
        ? SystemUiOverlayStyle.light
        : SystemUiOverlayStyle.dark,
      child: Scaffold(
        backgroundColor: backgroundColor ?? context.backgroundColor,
        appBar: showAppBar 
          ? EnhancedAppBar(
              title: title,
              actions: actions,
              showBackButton: showBackButton,
            )
          : null,
        body: SafeArea(
          bottom: !hasBottomNavigation, // Don't add bottom safe area if nav bar exists
          child: Container(
            // Add bottom padding to account for 70px navigation bar
            padding: EdgeInsets.only(
              bottom: hasBottomNavigation ? 70 : 0,
            ),
            child: _buildBody(context),
          ),
        ),
        floatingActionButton: floatingActionButton,
        bottomSheet: bottomSheet,
      ),
    );
  }

  Widget _buildBody(BuildContext context) {
    if (isLoading) {
      return EnhancedLoading(
        message: S.of(context).loading,
      );
    }

    if (errorMessage != null) {
      return EnhancedError(
        message: errorMessage!,
        onRetry: onRetry,
      );
    }

    if (padding != null) {
      return Padding(
        padding: padding!,
        child: body,
      );
    }

    return body;
  }
}

/// Enhanced Scrollable Page Layout
class EnhancedScrollablePageLayout extends StatelessWidget {
  final String? title;
  final List<Widget> children;
  final List<Widget>? actions;
  final Widget? floatingActionButton;
  final bool showAppBar;
  final bool showBackButton;
  final bool isLoading;
  final String? errorMessage;
  final VoidCallback? onRetry;
  final EdgeInsetsGeometry? padding;
  final bool hasBottomNavigation;
  final Color? backgroundColor;
  final Future<void> Function()? onRefresh;
  final ScrollController? scrollController;
  final Widget? header;
  final Widget? footer;

  const EnhancedScrollablePageLayout({
    super.key,
    this.title,
    required this.children,
    this.actions,
    this.floatingActionButton,
    this.showAppBar = true,
    this.showBackButton = true,
    this.isLoading = false,
    this.errorMessage,
    this.onRetry,
    this.padding,
    this.hasBottomNavigation = true,
    this.backgroundColor,
    this.onRefresh,
    this.scrollController,
    this.header,
    this.footer,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: isDark 
        ? SystemUiOverlayStyle.light
        : SystemUiOverlayStyle.dark,
      child: Scaffold(
        backgroundColor: backgroundColor ?? context.backgroundColor,
        appBar: showAppBar 
          ? EnhancedAppBar(
              title: title,
              actions: actions,
              showBackButton: showBackButton,
            )
          : null,
        body: SafeArea(
          bottom: !hasBottomNavigation,
          child: _buildScrollableBody(context),
        ),
        floatingActionButton: floatingActionButton,
      ),
    );
  }

  Widget _buildScrollableBody(BuildContext context) {
    if (isLoading) {
      return EnhancedLoading(
        message: S.of(context).loading,
      );
    }

    if (errorMessage != null) {
      return EnhancedError(
        message: errorMessage!,
        onRetry: onRetry,
      );
    }

    Widget scrollView = CustomScrollView(
      controller: scrollController,
      slivers: [
        // Header
        if (header != null)
          SliverToBoxAdapter(child: header!),
        
        // Main content
        SliverPadding(
          padding: padding ?? const EdgeInsets.all(16),
          sliver: SliverList(
            delegate: SliverChildListDelegate(children),
          ),
        ),
        
        // Footer
        if (footer != null)
          SliverToBoxAdapter(child: footer!),
        
        // Bottom navigation spacing
        if (hasBottomNavigation)
          const SliverToBoxAdapter(
            child: SizedBox(height: 70),
          ),
      ],
    );

    if (onRefresh != null) {
      return RefreshIndicator(
        onRefresh: onRefresh!,
        color: context.accentColor,
        backgroundColor: context.cardColor,
        child: scrollView,
      );
    }

    return scrollView;
  }
}

/// Enhanced Tab Page Layout
class EnhancedTabPageLayout extends StatelessWidget {
  final String? title;
  final List<String> tabs;
  final List<Widget> tabViews;
  final List<Widget>? actions;
  final bool showAppBar;
  final bool showBackButton;
  final bool hasBottomNavigation;
  final Color? backgroundColor;
  final TabController? controller;

  const EnhancedTabPageLayout({
    super.key,
    this.title,
    required this.tabs,
    required this.tabViews,
    this.actions,
    this.showAppBar = true,
    this.showBackButton = true,
    this.hasBottomNavigation = true,
    this.backgroundColor,
    this.controller,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: isDark 
        ? SystemUiOverlayStyle.light
        : SystemUiOverlayStyle.dark,
      child: Scaffold(
        backgroundColor: backgroundColor ?? context.backgroundColor,
        appBar: showAppBar 
          ? EnhancedAppBar(
              title: title,
              actions: actions,
              showBackButton: showBackButton,
            )
          : null,
        body: SafeArea(
          bottom: !hasBottomNavigation,
          child: Column(
            children: [
              // Tab Bar
              Container(
                margin: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: context.cardColor,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: context.secondaryTextColor.withValues(alpha: 0.2),
                  ),
                ),
                child: TabBar(
                  controller: controller,
                  indicator: BoxDecoration(
                    color: context.accentColor,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  labelColor: Colors.white,
                  unselectedLabelColor: context.secondaryTextColor,
                  labelStyle: AppTextStyles.font14SemiBold,
                  unselectedLabelStyle: AppTextStyles.font14Regular,
                  tabs: tabs.map((tab) => Tab(text: tab)).toList(),
                  dividerColor: Colors.transparent,
                  indicatorSize: TabBarIndicatorSize.tab,
                  padding: const EdgeInsets.all(4),
                ),
              ),
              // Tab Views
              Expanded(
                child: Container(
                  // Add bottom padding for navigation bar
                  padding: EdgeInsets.only(
                    bottom: hasBottomNavigation ? 70 : 0,
                  ),
                  child: TabBarView(
                    controller: controller,
                    children: tabViews,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Enhanced Modal Page Layout (for full-screen modals)
class EnhancedModalPageLayout extends StatelessWidget {
  final String? title;
  final Widget body;
  final List<Widget>? actions;
  final bool showCloseButton;
  final VoidCallback? onClose;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;

  const EnhancedModalPageLayout({
    super.key,
    this.title,
    required this.body,
    this.actions,
    this.showCloseButton = true,
    this.onClose,
    this.padding,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: isDark 
        ? SystemUiOverlayStyle.light
        : SystemUiOverlayStyle.dark,
      child: Scaffold(
        backgroundColor: backgroundColor ?? context.backgroundColor,
        appBar: AppBar(
          title: title != null 
            ? Text(
                title!,
                style: AppTextStyles.font18Bold.copyWith(
                  color: context.primaryTextColor,
                ),
              )
            : null,
          backgroundColor: context.backgroundColor,
          elevation: 0,
          leading: showCloseButton
            ? IconButton(
                onPressed: onClose ?? () => Navigator.pop(context),
                icon: Icon(
                  Icons.close_rounded,
                  color: context.primaryTextColor,
                ),
              )
            : null,
          actions: actions,
        ),
        body: SafeArea(
          child: padding != null 
            ? Padding(padding: padding!, child: body)
            : body,
        ),
      ),
    );
  }
}
