import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/widgets/enhanced_layouts.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/generated/l10n.dart';

/// Enhanced Page Template with all best practices
class EnhancedPageTemplate extends StatefulWidget {
  final String title;
  final Widget body;
  final List<Widget>? actions;
  final Widget? floatingActionButton;
  final bool showBackButton;
  final bool isLoading;
  final String? errorMessage;
  final VoidCallback? onRetry;
  final EdgeInsetsGeometry? padding;
  final bool showAppBar;

  const EnhancedPageTemplate({
    super.key,
    required this.title,
    required this.body,
    this.actions,
    this.floatingActionButton,
    this.showBackButton = true,
    this.isLoading = false,
    this.errorMessage,
    this.onRetry,
    this.padding,
    this.showAppBar = true,
  });

  @override
  State<EnhancedPageTemplate> createState() => _EnhancedPageTemplateState();
}

class _EnhancedPageTemplateState extends State<EnhancedPageTemplate>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: isDark 
        ? SystemUiOverlayStyle.light
        : SystemUiOverlayStyle.dark,
      child: EnhancedScaffold(
        backgroundColor: context.backgroundColor,
        appBar: widget.showAppBar 
          ? EnhancedAppBar(
              title: widget.title,
              actions: widget.actions,
              showBackButton: widget.showBackButton,
            )
          : null,
        body: FadeTransition(
          opacity: _fadeAnimation,
          child: _buildBody(),
        ),
        floatingActionButton: widget.floatingActionButton,
        padding: widget.padding,
      ),
    );
  }

  Widget _buildBody() {
    if (widget.isLoading) {
      return EnhancedLoading(
        message: S.of(context).loading,
      );
    }

    if (widget.errorMessage != null) {
      return EnhancedError(
        message: widget.errorMessage!,
        onRetry: widget.onRetry,
      );
    }

    return widget.body;
  }
}

/// Enhanced List View with pull-to-refresh and pagination
class EnhancedListView<T> extends StatefulWidget {
  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final Future<void> Function()? onRefresh;
  final Future<void> Function()? onLoadMore;
  final bool hasMore;
  final bool isLoading;
  final String? emptyTitle;
  final String? emptySubtitle;
  final IconData? emptyIcon;
  final EdgeInsetsGeometry? padding;
  final Widget? header;
  final Widget? footer;

  const EnhancedListView({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.onRefresh,
    this.onLoadMore,
    this.hasMore = false,
    this.isLoading = false,
    this.emptyTitle,
    this.emptySubtitle,
    this.emptyIcon,
    this.padding,
    this.header,
    this.footer,
  });

  @override
  State<EnhancedListView<T>> createState() => _EnhancedListViewState<T>();
}

class _EnhancedListViewState<T> extends State<EnhancedListView<T>> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (widget.hasMore && !widget.isLoading && widget.onLoadMore != null) {
        widget.onLoadMore!();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.items.isEmpty && !widget.isLoading) {
      return EnhancedEmptyState(
        icon: widget.emptyIcon ?? Icons.inbox_outlined,
        title: widget.emptyTitle ?? S.of(context).noResults,
        subtitle: widget.emptySubtitle,
      );
    }

    Widget listView = ListView.builder(
      controller: _scrollController,
      padding: widget.padding ?? const EdgeInsets.all(16),
      physics: const AlwaysScrollableScrollPhysics(),
      itemCount: _getItemCount(),
      itemBuilder: (context, index) {
        // Header
        if (widget.header != null && index == 0) {
          return widget.header!;
        }

        // Adjust index for header
        final adjustedIndex = widget.header != null ? index - 1 : index;

        // Items
        if (adjustedIndex < widget.items.length) {
          return AnimatedContainer(
            duration: Duration(milliseconds: 300 + (adjustedIndex * 50)),
            curve: Curves.easeOutBack,
            child: widget.itemBuilder(context, widget.items[adjustedIndex], adjustedIndex),
          );
        }

        // Loading indicator
        if (widget.isLoading && widget.hasMore) {
          return const Padding(
            padding: EdgeInsets.all(16),
            child: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        // Footer
        if (widget.footer != null) {
          return widget.footer!;
        }

        return const SizedBox.shrink();
      },
    );

    if (widget.onRefresh != null) {
      return RefreshIndicator(
        onRefresh: widget.onRefresh!,
        color: context.accentColor,
        backgroundColor: context.cardColor,
        child: listView,
      );
    }

    return listView;
  }

  int _getItemCount() {
    int count = widget.items.length;
    
    if (widget.header != null) count++;
    if (widget.isLoading && widget.hasMore) count++;
    if (widget.footer != null) count++;
    
    return count;
  }
}

/// Enhanced Grid View
class EnhancedGridView<T> extends StatelessWidget {
  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final int crossAxisCount;
  final double childAspectRatio;
  final double crossAxisSpacing;
  final double mainAxisSpacing;
  final EdgeInsetsGeometry? padding;
  final String? emptyTitle;
  final String? emptySubtitle;
  final IconData? emptyIcon;

  const EnhancedGridView({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.crossAxisCount = 2,
    this.childAspectRatio = 1.0,
    this.crossAxisSpacing = 16,
    this.mainAxisSpacing = 16,
    this.padding,
    this.emptyTitle,
    this.emptySubtitle,
    this.emptyIcon,
  });

  @override
  Widget build(BuildContext context) {
    if (items.isEmpty) {
      return EnhancedEmptyState(
        icon: emptyIcon ?? Icons.grid_view_outlined,
        title: emptyTitle ?? S.of(context).noResults,
        subtitle: emptySubtitle,
      );
    }

    return GridView.builder(
      padding: padding ?? const EdgeInsets.all(16),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: childAspectRatio,
        crossAxisSpacing: crossAxisSpacing,
        mainAxisSpacing: mainAxisSpacing,
      ),
      itemCount: items.length,
      itemBuilder: (context, index) {
        return AnimatedContainer(
          duration: Duration(milliseconds: 300 + (index * 50)),
          curve: Curves.easeOutBack,
          child: itemBuilder(context, items[index], index),
        );
      },
    );
  }
}

/// Enhanced Tab View
class EnhancedTabView extends StatelessWidget {
  final List<String> tabs;
  final List<Widget> children;
  final TabController? controller;
  final bool isScrollable;
  final EdgeInsetsGeometry? padding;

  const EnhancedTabView({
    super.key,
    required this.tabs,
    required this.children,
    this.controller,
    this.isScrollable = false,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          margin: padding ?? const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: context.cardColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: context.secondaryTextColor.withValues(alpha: 0.2),
            ),
          ),
          child: TabBar(
            controller: controller,
            isScrollable: isScrollable,
            indicator: BoxDecoration(
              color: context.accentColor,
              borderRadius: BorderRadius.circular(8),
            ),
            labelColor: Colors.white,
            unselectedLabelColor: context.secondaryTextColor,
            labelStyle: AppTextStyles.font14SemiBold,
            unselectedLabelStyle: AppTextStyles.font14Regular,
            tabs: tabs.map((tab) => Tab(text: tab)).toList(),
            dividerColor: Colors.transparent,
            indicatorSize: TabBarIndicatorSize.tab,
            padding: const EdgeInsets.all(4),
          ),
        ),
        Expanded(
          child: TabBarView(
            controller: controller,
            children: children,
          ),
        ),
      ],
    );
  }
}
