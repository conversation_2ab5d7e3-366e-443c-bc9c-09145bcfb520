import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_colors.dart';

/// Helper class for theme-aware colors and styling
class ThemeHelper {
  
  /// Get theme-aware text color for primary content
  static Color getPrimaryTextColor(BuildContext context) {
    final theme = Theme.of(context);
    return theme.textTheme.bodyLarge?.color ?? 
           (theme.brightness == Brightness.dark ? AppColors.white : AppColors.black);
  }

  /// Get theme-aware text color for secondary content
  static Color getSecondaryTextColor(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    return isDark ? AppColors.lightGrey : AppColors.darkGrey2;
  }

  /// Get theme-aware text color for captions and hints
  static Color getCaptionTextColor(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    return isDark ? AppColors.lightGrey3 : AppColors.darkGrey3;
  }

  /// Get theme-aware accent color
  static Color getAccentColor(BuildContext context) {
    // Use yellow as the main accent color for both light and dark modes
    return AppColors.yellow;
  }

  /// Get theme-aware card background color
  static Color getCardColor(BuildContext context) {
    return Theme.of(context).cardColor;
  }

  /// Get theme-aware background color
  static Color getBackgroundColor(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    return isDark ? AppColors.black : const Color(0xFFF5F6FA);
  }

  /// Get theme-aware shadow color
  static Color getShadowColor(BuildContext context, {double opacity = 0.1}) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    return isDark 
        ? Colors.black.withValues(alpha: opacity * 3) // Stronger shadows in dark mode
        : Colors.black.withValues(alpha: opacity);
  }

  /// Get theme-aware icon color
  static Color getIconColor(BuildContext context) {
    return Theme.of(context).iconTheme.color ?? getPrimaryTextColor(context);
  }

  /// Get theme-aware border color
  static Color getBorderColor(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    return isDark ? AppColors.darkGrey : AppColors.lightGrey3;
  }

  /// Get theme-aware success color
  static Color getSuccessColor(BuildContext context) {
    return Colors.green;
  }

  /// Get theme-aware warning color
  static Color getWarningColor(BuildContext context) {
    return Colors.amber;
  }

  /// Get theme-aware error color
  static Color getErrorColor(BuildContext context) {
    return Colors.red;
  }

  /// Get theme-aware info color
  static Color getInfoColor(BuildContext context) {
    return Colors.blue;
  }

  /// Check if current theme is dark
  static bool isDarkMode(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark;
  }

  /// Get theme-aware elevation for cards
  static double getCardElevation(BuildContext context) {
    final isDark = isDarkMode(context);
    return isDark ? 8.0 : 4.0; // Higher elevation in dark mode for better visibility
  }

  /// Get theme-aware box shadow for cards
  static List<BoxShadow> getCardShadow(BuildContext context) {
    final isDark = isDarkMode(context);
    return [
      BoxShadow(
        color: getShadowColor(context, opacity: isDark ? 0.3 : 0.08),
        blurRadius: 20,
        offset: const Offset(0, 8),
        spreadRadius: 0,
      ),
      BoxShadow(
        color: getShadowColor(context, opacity: isDark ? 0.2 : 0.04),
        blurRadius: 6,
        offset: const Offset(0, 2),
        spreadRadius: 0,
      ),
    ];
  }

  /// Get theme-aware button shadow
  static List<BoxShadow> getButtonShadow(BuildContext context) {
    final isDark = isDarkMode(context);
    return [
      BoxShadow(
        color: getShadowColor(context, opacity: isDark ? 0.4 : 0.15),
        blurRadius: 8,
        offset: const Offset(0, 2),
      ),
    ];
  }

  /// Get theme-aware surface color with opacity
  static Color getSurfaceColor(BuildContext context, {double opacity = 1.0}) {
    final theme = Theme.of(context);
    return theme.colorScheme.surface.withValues(alpha: opacity);
  }

  /// Get theme-aware overlay color
  static Color getOverlayColor(BuildContext context, {double opacity = 0.1}) {
    final isDark = isDarkMode(context);
    return isDark 
        ? Colors.white.withValues(alpha: opacity)
        : Colors.black.withValues(alpha: opacity);
  }

  /// Get theme-aware divider color
  static Color getDividerColor(BuildContext context) {
    return Theme.of(context).dividerColor;
  }

  /// Get theme-aware app bar colors
  static AppBarTheme getAppBarTheme(BuildContext context) {
    return Theme.of(context).appBarTheme;
  }

  /// Get adaptive color based on theme
  static Color getAdaptiveColor(BuildContext context, {
    required Color lightColor,
    required Color darkColor,
  }) {
    final isDark = isDarkMode(context);
    return isDark ? darkColor : lightColor;
  }

  /// Get theme-aware text style with color
  static TextStyle getThemedTextStyle(
    BuildContext context,
    TextStyle baseStyle, {
    Color? lightColor,
    Color? darkColor,
  }) {
    final isDark = isDarkMode(context);
    Color textColor;
    
    if (lightColor != null && darkColor != null) {
      textColor = isDark ? darkColor : lightColor;
    } else {
      textColor = getPrimaryTextColor(context);
    }
    
    return baseStyle.copyWith(color: textColor);
  }

  /// Get theme-aware container decoration
  static BoxDecoration getCardDecoration(BuildContext context, {
    double borderRadius = 16,
    bool withShadow = true,
  }) {
    return BoxDecoration(
      color: getCardColor(context),
      borderRadius: BorderRadius.circular(borderRadius),
      boxShadow: withShadow ? getCardShadow(context) : null,
    );
  }

  /// Get theme-aware input decoration
  static InputDecoration getInputDecoration(
    BuildContext context, {
    String? hintText,
    Widget? prefixIcon,
    Widget? suffixIcon,
  }) {
    return InputDecoration(
      hintText: hintText,
      hintStyle: TextStyle(color: getCaptionTextColor(context)),
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      filled: true,
      fillColor: getCardColor(context),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: getBorderColor(context)),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: getBorderColor(context)),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: getAccentColor(context), width: 2),
      ),
    );
  }
}

/// Extension on BuildContext for easy theme access
extension ThemeExtension on BuildContext {
  bool get isDarkMode => ThemeHelper.isDarkMode(this);
  Color get primaryTextColor => ThemeHelper.getPrimaryTextColor(this);
  Color get secondaryTextColor => ThemeHelper.getSecondaryTextColor(this);
  Color get captionTextColor => ThemeHelper.getCaptionTextColor(this);
  Color get accentColor => ThemeHelper.getAccentColor(this);
  Color get cardColor => ThemeHelper.getCardColor(this);
  Color get backgroundColor => ThemeHelper.getBackgroundColor(this);
  Color get iconColor => ThemeHelper.getIconColor(this);
  List<BoxShadow> get cardShadow => ThemeHelper.getCardShadow(this);
}
