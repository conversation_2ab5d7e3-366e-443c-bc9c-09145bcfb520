import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/typography_helper.dart';

/// Examples of how to use the new Arabic-English typography system
class TypographyExamples extends StatelessWidget {
  const TypographyExamples({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Typography Examples'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Smart Typography Examples
            _buildSection('Smart Typography (Auto-detects language)', [
              TypographyHelper.smartText(
                'Welcome to Gather Point',
                isLargeHeading: true,
                color: AppColors.black,
              ),
              const SizedBox(height: 8),
              TypographyHelper.smartText(
                'مرحباً بكم في نقطة التجمع',
                isLargeHeading: true,
                color: AppColors.black,
              ),
              const SizedBox(height: 16),
              TypographyHelper.smartText(
                'This is English body text with optimized spacing.',
                color: AppColors.darkGrey2,
              ),
              const SizedBox(height: 8),
              TypographyHelper.smartText(
                'هذا نص عربي مع تباعد محسن للقراءة.',
                color: AppColors.darkGrey2,
              ),
            ]),

            const SizedBox(height: 30),

            // Manual Typography Examples
            _buildSection('Manual Typography Selection', [
              Text(
                'English Heading',
                style: AppTextStyles.englishHeadingLarge.copyWith(color: AppColors.black),
              ),
              const SizedBox(height: 8),
              Text(
                'عنوان عربي',
                style: AppTextStyles.arabicHeadingLarge.copyWith(color: AppColors.black),
              ),
              const SizedBox(height: 16),
              Text(
                'English body text with perfect letter spacing.',
                style: AppTextStyles.englishBodyText.copyWith(color: AppColors.darkGrey2),
              ),
              const SizedBox(height: 8),
              Text(
                'نص عربي مع تباعد مثالي للحروف.',
                style: AppTextStyles.arabicBodyText.copyWith(color: AppColors.darkGrey2),
              ),
            ]),

            const SizedBox(height: 30),

            // Standard Typography Examples
            _buildSection('Standard Noto Sans Typography', [
              Text(
                'Large Heading',
                style: AppTextStyles.font28Bold.copyWith(color: AppColors.black),
              ),
              const SizedBox(height: 8),
              Text(
                'Medium Heading',
                style: AppTextStyles.font20Bold.copyWith(color: AppColors.black),
              ),
              const SizedBox(height: 8),
              Text(
                'Regular Body Text',
                style: AppTextStyles.font16Regular.copyWith(color: AppColors.darkGrey2),
              ),
              const SizedBox(height: 8),
              Text(
                'Caption Text',
                style: AppTextStyles.font12Regular.copyWith(color: AppColors.darkGrey3),
              ),
            ]),

            const SizedBox(height: 30),

            // String Extension Examples
            _buildSection('String Extension Usage', [
              Text(
                'English text',
                style: AppTextStyles.font20Bold.copyWith(color: AppColors.black),
              ),
              const SizedBox(height: 8),
              Text(
                'نص عربي',
                style: AppTextStyles.font20Bold.copyWith(color: AppColors.black),
              ),
              const SizedBox(height: 16),
              Text(
                'Body text example',
                style: AppTextStyles.font14Regular.copyWith(color: AppColors.darkGrey2),
              ),
              const SizedBox(height: 8),
              Text(
                'مثال على نص الجسم',
                style: AppTextStyles.font14Regular.copyWith(color: AppColors.darkGrey2),
              ),
            ]),

            const SizedBox(height: 30),

            // Language Detection Examples
            _buildSection('Language Detection', [
              _buildLanguageExample('Hello World'),
              _buildLanguageExample('مرحبا بالعالم'),
              _buildLanguageExample('Mixed text مختلط'),
              _buildLanguageExample('123 Numbers'),
              _buildLanguageExample('أرقام 123'),
            ]),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTextStyles.font18Bold.copyWith(color: AppColors.brown),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.lightGrey,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: AppColors.lightGrey3),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: children,
          ),
        ),
      ],
    );
  }

  Widget _buildLanguageExample(String text) {
    final isArabic = TypographyHelper.isArabicText(text);
    final direction = TypographyHelper.getTextDirection(text);

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              '"$text"',
              style: AppTextStyles.font14Regular.copyWith(color: AppColors.black),
            ),
          ),
          Expanded(
            child: Text(
              isArabic ? 'Arabic' : 'English',
              style: AppTextStyles.font12Regular.copyWith(color: AppColors.darkGrey3),
            ),
          ),
          Expanded(
            child: Text(
              direction == TextDirection.rtl ? 'RTL' : 'LTR',
              style: AppTextStyles.font12Regular.copyWith(color: AppColors.darkGrey3),
            ),
          ),
        ],
      ),
    );
  }
}
