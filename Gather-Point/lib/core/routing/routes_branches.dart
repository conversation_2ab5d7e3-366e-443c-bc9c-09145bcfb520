import 'package:flutter/material.dart';
import 'package:gather_point/core/routing/routes_keys.dart';
import 'package:gather_point/core/utils/app_constants.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/home/<USER>/views/home_test.dart';
import 'package:gather_point/feature/host/presentation/views/host_home_page.dart';
import 'package:gather_point/feature/profile/presentation/views/my_bookings_screen.dart';
import 'package:gather_point/feature/profile/presentation/views/my_listing_screen.dart';
import 'package:gather_point/feature/profile/presentation/views/profile_view.dart';
import 'package:gather_point/feature/reels/presentation/views/reels_page.dart';
import 'package:gather_point/feature/search/presentation/views/search_screen.dart';
import 'package:go_router/go_router.dart';
import 'package:hive/hive.dart';

final userBox = Hive.box<UserEntity>(AppConstants.kMyProfileBoxName);
final ValueNotifier<bool> isHosterModeNotifier = ValueNotifier(
  userBox.get(AppConstants.kMyProfileKey)?.isHosterMode ?? false,
);

List<StatefulShellBranch> routesBranches() {
  return [
    StatefulShellBranch(
      routes: <RouteBase>[
        GoRoute(
          path: isHosterModeNotifier.value
              ? RoutesKeys.kHostHomeViewTab
              : RoutesKeys.kHomeViewTab,
          builder: (context, state) => isHosterModeNotifier.value
              ? const HostHomePage()
              : const GatherPointHome(),
        ),
      ],
    ),
    StatefulShellBranch(
      routes: <RouteBase>[
        GoRoute(
          path: isHosterModeNotifier.value
              ? RoutesKeys.kBookingsViewTab
              : RoutesKeys.kSearchViewTab,
          builder: (context, state) => isHosterModeNotifier.value
              ? const MyBookingsScreen()
              : const SearchScreen(),
        ),
      ],
    ),
    StatefulShellBranch(
      routes: <RouteBase>[
        GoRoute(
          path: isHosterModeNotifier.value
              ? RoutesKeys.kListingViewTab
              : RoutesKeys.kFavoritesViewTab,
          builder: (context, state) => isHosterModeNotifier.value
              ? const MyListingScreen()
              : const ReelsPage(
                  searchResults: [],
                  searchQuery: "",
                  serviceCategoryId: 1,
                ),
        ),
      ],
    ),
    StatefulShellBranch(
      routes: <RouteBase>[
        GoRoute(
          path: isHosterModeNotifier.value
              ? RoutesKeys.kProfileViewTab
              : RoutesKeys.kProfileViewTab,
          builder: (context, state) => isHosterModeNotifier.value
              ? const ProfileTabView()
              : const ProfileTabView(),
        ),
      ],
    ),
  ];
}

ValueListenableBuilder<bool> buildRoutesBranches() {
  return ValueListenableBuilder<bool>(
    valueListenable: isHosterModeNotifier,
    builder: (context, isHosterMode, child) {
      return Container(); // Replace with an appropriate widget or UI representation
    },
  );
}
