import 'package:flutter/foundation.dart';
import 'package:gather_point/core/routing/routes_keys.dart';
import 'package:gather_point/core/routing/routes_branches.dart';

/// Helper class for debugging routing issues
class RouteDebugHelper {
  /// Print all available routes for debugging
  static void printAvailableRoutes() {
    if (kDebugMode) {
      print('🔍 Available Routes:');
      print('  - Home: ${RoutesKeys.kHomeViewTab}');
      print('  - Host Home: ${RoutesKeys.kHostHomeViewTab}');
      print('  - Search: ${RoutesKeys.kSearchViewTab}');
      print('  - Bookings: ${RoutesKeys.kBookingsViewTab}');
      print('  - Favorites: ${RoutesKeys.kFavoritesViewTab}');
      print('  - Listings: ${RoutesKeys.kListingViewTab}');
      print('  - Profile: ${RoutesKeys.kProfileViewTab}');
      print('  - Splash: ${RoutesKeys.kSplashView}');
      print('  - Login: ${RoutesKeys.kLoginView}');
      print('  - Onboarding: ${RoutesKeys.kOnBoardingView}');
    }
  }

  /// Print current hoster mode status
  static void printHosterModeStatus() {
    if (kDebugMode) {
      print('🏠 Hoster Mode: ${isHosterModeNotifier.value}');
      print('   Current routes will show: ${isHosterModeNotifier.value ? "Host" : "Guest"} interface');
    }
  }

  /// Print route navigation attempt
  static void printNavigationAttempt(String route) {
    if (kDebugMode) {
      print('🧭 Navigation attempt to: $route');
      print('   Hoster mode: ${isHosterModeNotifier.value}');
      
      // Check if route exists
      final validRoutes = [
        RoutesKeys.kHomeViewTab,
        RoutesKeys.kHostHomeViewTab,
        RoutesKeys.kSearchViewTab,
        RoutesKeys.kBookingsViewTab,
        RoutesKeys.kFavoritesViewTab,
        RoutesKeys.kListingViewTab,
        RoutesKeys.kProfileViewTab,
        RoutesKeys.kSplashView,
        RoutesKeys.kLoginView,
        RoutesKeys.kOnBoardingView,
        RoutesKeys.kVerifyOTPView,
        RoutesKeys.kNoInternetView,
      ];
      
      if (validRoutes.contains(route)) {
        print('   ✅ Route exists');
      } else {
        print('   ❌ Route does not exist!');
        print('   💡 Available routes:');
        for (final validRoute in validRoutes) {
          print('      - $validRoute');
        }
      }
    }
  }

  /// Toggle hoster mode for testing
  static void toggleHosterMode() {
    isHosterModeNotifier.value = !isHosterModeNotifier.value;
    if (kDebugMode) {
      print('🔄 Hoster mode toggled to: ${isHosterModeNotifier.value}');
    }
  }

  /// Get the correct route for current mode
  static String getCorrectHomeRoute() {
    return isHosterModeNotifier.value 
        ? RoutesKeys.kHostHomeViewTab 
        : RoutesKeys.kHomeViewTab;
  }

  /// Get the correct search/bookings route for current mode
  static String getCorrectSecondTabRoute() {
    return isHosterModeNotifier.value 
        ? RoutesKeys.kBookingsViewTab 
        : RoutesKeys.kSearchViewTab;
  }

  /// Get the correct favorites/listings route for current mode
  static String getCorrectThirdTabRoute() {
    return isHosterModeNotifier.value 
        ? RoutesKeys.kListingViewTab 
        : RoutesKeys.kFavoritesViewTab;
  }
}

/// Extension to add debugging to GoRouter
extension GoRouterDebugExtension on Object {
  /// Debug navigation with logging
  void debugNavigation(String route) {
    RouteDebugHelper.printNavigationAttempt(route);
  }
}

/// Usage Examples:
/// 
/// ```dart
/// // Print all available routes
/// RouteDebugHelper.printAvailableRoutes();
/// 
/// // Check hoster mode status
/// RouteDebugHelper.printHosterModeStatus();
/// 
/// // Debug navigation attempt
/// RouteDebugHelper.printNavigationAttempt('/homeViewTab');
/// 
/// // Toggle hoster mode for testing
/// RouteDebugHelper.toggleHosterMode();
/// 
/// // Get correct routes for current mode
/// final homeRoute = RouteDebugHelper.getCorrectHomeRoute();
/// context.go(homeRoute);
/// ```
