import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_colors.dart';

/// 🌓 Light Theme
final ThemeData lightTheme = ThemeData(
  brightness: Brightness.light,
  scaffoldBackgroundColor: AppColors.white,
  cardColor: AppColors.white,
  appBarTheme: const AppBarTheme(
    color: AppColors.white,  // White background for light mode
    iconTheme: IconThemeData(color: AppColors.black),  // Black icons
    titleTextStyle: TextStyle(
      color: AppColors.black,  // Black text
      fontSize: 20,
      fontWeight: FontWeight.bold,
    ),
    elevation: 0,  // Flat design
    surfaceTintColor: Colors.transparent,
  ),
  textTheme: const TextTheme(
    displayLarge: TextStyle(
      fontFamily: 'HacenTunisia',
      color: AppColors.black,
      fontWeight: FontWeight.bold,
    ),
    displayMedium: TextStyle(
      fontFamily: 'HacenTunisia',
      color: AppColors.black,
    ),
    bodyLarge: TextStyle(
      fontFamily: 'HacenTunisia',
      color: AppColors.black,
    ),
    bodyMedium: TextStyle(
      fontFamily: 'HacenTunisia',
      color: AppColors.black,
    ),
    labelLarge: TextStyle(
      fontFamily: 'HacenTunisia',
      color: AppColors.black,
    ),
  ),
  iconTheme: const IconThemeData(color: AppColors.black),
  elevatedButtonTheme: ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      backgroundColor: AppColors.yellow,
      foregroundColor: AppColors.black,
      textStyle: const TextStyle(fontWeight: FontWeight.bold),
    ),
  ),
  textButtonTheme: TextButtonThemeData(
    style: TextButton.styleFrom(
      foregroundColor: AppColors.black,
    ),
  ),
  inputDecorationTheme: InputDecorationTheme(
    filled: true,
    fillColor: AppColors.white,
    border: OutlineInputBorder(
      borderSide: BorderSide(color: AppColors.black.withValues(alpha: 0.7)),
      borderRadius: BorderRadius.circular(12),
    ),
    enabledBorder: OutlineInputBorder(
      borderSide: BorderSide(color: AppColors.black.withValues(alpha: 0.5)),
      borderRadius: BorderRadius.circular(12),
    ),
    focusedBorder: OutlineInputBorder(
      borderSide: const BorderSide(color: AppColors.yellow, width: 2),
      borderRadius: BorderRadius.circular(12),
    ),
    labelStyle: const TextStyle(color: AppColors.black),
    hintStyle: const TextStyle(color: AppColors.lightGrey),
  ),
  sliderTheme: SliderThemeData(
    activeTrackColor: AppColors.yellow,
    inactiveTrackColor: Colors.grey[300],
    thumbColor: AppColors.yellow,
    overlayColor: AppColors.yellow.withValues(alpha: 0.3),
  ),
  colorScheme: const ColorScheme.light().copyWith(
    primary: AppColors.yellow,  // Use yellow as primary in light mode too
    secondary: AppColors.yellow,
    surface: AppColors.white,
    onPrimary: AppColors.black,  // Black text on yellow background
    onSecondary: AppColors.black,
  ),
);

/// 🌑 Dark Theme
final ThemeData darkTheme = ThemeData(
  brightness: Brightness.dark,
  scaffoldBackgroundColor: AppColors.black,
  cardColor: AppColors.darkGrey,
  appBarTheme: const AppBarTheme(
    color: AppColors.yellow,
    iconTheme: IconThemeData(color: AppColors.black),
    titleTextStyle: TextStyle(
      color: AppColors.black,
      fontSize: 20,
      fontWeight: FontWeight.bold,
    ),
  ),
  textTheme: const TextTheme(
    displayLarge: TextStyle(
      fontFamily: 'HacenTunisia',
      color: AppColors.white,
      fontWeight: FontWeight.bold,
    ),
    displayMedium: TextStyle(
      fontFamily: 'HacenTunisia',
      color: AppColors.white,
    ),
    bodyLarge: TextStyle(
      fontFamily: 'HacenTunisia',
      color: AppColors.white,
    ),
    bodyMedium: TextStyle(
      fontFamily: 'HacenTunisia',
      color: AppColors.white,
    ),
    labelLarge: TextStyle(
      fontFamily: 'HacenTunisia',
      color: AppColors.white,
    ),
  ),
  iconTheme: const IconThemeData(color: AppColors.white),
  elevatedButtonTheme: ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      backgroundColor: AppColors.yellow,
      foregroundColor: AppColors.black,
      textStyle: const TextStyle(fontWeight: FontWeight.bold),
    ),
  ),
  textButtonTheme: TextButtonThemeData(
    style: TextButton.styleFrom(
      foregroundColor: AppColors.white,
    ),
  ),
  inputDecorationTheme: InputDecorationTheme(
    filled: true,
    fillColor: AppColors.darkGrey,
    border: OutlineInputBorder(
      borderSide: BorderSide(color: AppColors.yellow.withValues(alpha: 0.8)),
      borderRadius: BorderRadius.circular(12),
    ),
    enabledBorder: OutlineInputBorder(
      borderSide: BorderSide(color: AppColors.yellow.withValues(alpha: 0.6)),
      borderRadius: BorderRadius.circular(12),
    ),
    focusedBorder: OutlineInputBorder(
      borderSide: const BorderSide(color: AppColors.yellow, width: 2),
      borderRadius: BorderRadius.circular(12),
    ),
    labelStyle: const TextStyle(color: AppColors.yellow),
    hintStyle: const TextStyle(color: Colors.grey),
  ),
  sliderTheme: SliderThemeData(
    activeTrackColor: AppColors.yellow,
    inactiveTrackColor: Colors.grey[600],
    thumbColor: AppColors.yellow,
    overlayColor: AppColors.yellow.withValues(alpha: 0.3),
  ),
  colorScheme: const ColorScheme.dark().copyWith(
    primary: AppColors.yellow,
    secondary: AppColors.yellow,
    surface: AppColors.darkGrey,
    onPrimary: AppColors.black,
    onSecondary: AppColors.black,
  ),
);
