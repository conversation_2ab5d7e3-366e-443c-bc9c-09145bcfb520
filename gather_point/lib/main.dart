import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return const MaterialApp(
      debugShowCheckedModeBanner: false,
      home: Scaffold(
        appBar: CustomAppBar(),
        body: Center(
          child: Text(
            "",
            style: TextStyle(fontSize: 18),
          ),
        ),
        bottomNavigationBar: CustomBottomNavigationBar(),
      ),
    );
  }
}

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  const CustomAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Container(
        height: preferredSize.height,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xFFFFC107), // Yellow
              Color(0xFF191919), // Black
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10.0,vertical: 10.0),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    hintText: "Search...",
                    hintStyle: const TextStyle(color: Colors.white70),
                    filled: true,
                    fillColor: Colors.black45,
                    prefixIcon: const Icon(Icons.search, color: Colors.white70),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(30.0),
                      borderSide: BorderSide.none,
                    ),
                  ),
                  style: const TextStyle(color: Colors.white,fontWeight: FontWeight.bold),
                ),
              ),
              const SizedBox(width: 10),
              AnimatedGradientBorderButton(
                onPressed: () {
                  print("User Icon Pressed!");
                },
                borderRadius: BorderRadius.circular(30),
                borderWidth: 3,
                gradientColors: const [
                  Colors.grey, // Black
                  Color(0xFFFFC107), // Yellow
                ],
                backgroundColor: const Color(0xFFFFFFFF),
                shadow: const BoxShadow(
                  color: Colors.black38,
                  blurRadius: 10,
                  offset: Offset(0, 5),
                ),
                child: const CircleAvatar(
                  backgroundImage: AssetImage('assets/user_profile.jpg'),
                  radius: 18,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(100.0); // Increased height
}

class CustomBottomNavigationBar extends StatelessWidget {
  const CustomBottomNavigationBar({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none, // Allow the button to overflow
      alignment: Alignment.center,
      children: [
        // Bottom bar
        Container(
          height: 90,
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Color(0xFF000000), // Black
                Color(0xFF333333), // Dark Gray
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black12,
                blurRadius: 10,
                spreadRadius: 5,
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Padding(
                padding: const EdgeInsets.only(bottom: 20.0),
                child: IconButton(
                  icon: Lottie.asset('assets/cart_animated.json'),
                  onPressed: () {
                    print("Home Pressed");
                  },
                ),
              ),
              const SizedBox(width: 60), // Spacer for center cutout
              Padding(
                padding: const EdgeInsets.only(bottom: 20.0),
                child: IconButton(
                  icon: Lottie.asset('assets/stack_animated.json'),
                  onPressed: () {
                    print("Notifications Pressed");
                  },
                ),
              ),
            ],
          ),
        ),
        // Cutout effect
        Positioned(
          bottom: 35, // Align cutout flush with the bottom bar
          child: ClipOval(
            child: Container(
              width: 105,
              height: 105,
              color: Colors.white, // Matches the bottom bar background
            ),
          ),
        ),
        // Floating center button
        Positioned(
          bottom: 45, // Raise the button slightly above the cutout
          child: AnimatedGradientBorderButton(
            onPressed: () {
              print("Center Button Pressed!");
            },
            borderRadius: BorderRadius.circular(50),
            borderWidth: 4,
            gradientColors: const [
              Color(0xFF191919), // Black
              Color(0xFFFFC107), // Yellow
            ],
            backgroundColor: const Color(0xFFFFC107),
            shadow: const BoxShadow(
              color: Colors.black38,
              blurRadius: 10,
              offset: Offset(0, 5),
            ),
            child: Image.asset(
              'assets/center_button_image.png', // Replace with your image asset path
              width: 40,
              height: 40,
              fit: BoxFit.contain,
            ),
          ),
        ),
      ],
    );
  }
}

class AnimatedGradientBorderButton extends StatefulWidget {
  final VoidCallback onPressed;
  final Widget child;
  final double borderWidth;
  final BorderRadius borderRadius;
  final List<Color> gradientColors;
  final Color backgroundColor;
  final BoxShadow shadow;

  const AnimatedGradientBorderButton({
    super.key,
    required this.onPressed,
    required this.child,
    required this.backgroundColor,
    this.borderWidth = 4.0,
    this.borderRadius = BorderRadius.zero,
    required this.gradientColors,
    this.shadow = const BoxShadow(
      color: Colors.black26,
      blurRadius: 8,
      offset: Offset(0, 4),
    ),
  });

  @override
  _AnimatedGradientBorderButtonState createState() =>
      _AnimatedGradientBorderButtonState();
}

class _AnimatedGradientBorderButtonState
    extends State<AnimatedGradientBorderButton> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 3),
    )..repeat();

    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: widget.gradientColors,
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              transform: GradientRotation(_animation.value * 2 * 3.14159),
            ),
            borderRadius: widget.borderRadius,
            boxShadow: [widget.shadow],
          ),
          padding: EdgeInsets.all(widget.borderWidth), // Border thickness
          child: Material(
            color: widget.backgroundColor, // Button background
            borderRadius: widget.borderRadius,
            child: InkWell(
              onTap: widget.onPressed,
              borderRadius: widget.borderRadius,
              child: Padding(
                padding: const EdgeInsets.all(16), // Inner padding for the child
                child: widget.child, // Icon or any child widget
              ),
            ),
          ),
        );
      },
    );
  }
}
